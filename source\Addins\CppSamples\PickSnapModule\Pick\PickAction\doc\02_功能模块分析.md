# 拾取Action功能模块分析

## 概述

本文档分析GDMPLab中拾取Action的各个功能模块，包括拾取过滤器、工具类、UI行为等组件的具体实现和功能特性。这些模块协同工作，为用户提供完整的三维对象拾取和选择功能。

## 核心功能模块

### 1. 拾取过滤器模块 (PickFilter)

#### 功能概述
拾取过滤器负责控制哪些元素和图形节点可以被拾取，提供细粒度的拾取控制机制。

#### 接口定义
```cpp
class IPickFilter : public IObject
{
public:
    // 判断是否允许拾取指定元素
    virtual bool AllowElement(const ElementId& elementId) const = 0;
    
    // 判断是否允许拾取指定图形节点
    virtual bool AllowGraphicsNode(const IGraphicsNodeReference& graphicsNodeReference) const = 0;
    
    // 设置拾取目标选项
    virtual bool SetPickTargetOption(IPickTarget* pickTarget) = 0;
};
```

#### 示例实现 (Sample::PickFilter)
```cpp
class PickFilter : public gcmp::IPickFilter
{
private:
    const gcmp::IDocument* m_pDocument;  // 关联的文档对象

public:
    // 元素过滤 - 示例中允许所有元素
    virtual bool AllowElement(const ElementId& elemId) const override
    {
        return true;  // 可扩展为复杂的过滤逻辑
    }
    
    // 图形节点过滤 - 示例中允许所有节点
    virtual bool AllowGraphicsNode(const IGraphicsNodeReference& nodeReference) const override
    {
        return true;  // 可扩展为基于节点类型的过滤
    }
    
    // 设置拾取目标 - 启用图形元素形状拾取
    virtual bool SetPickTargetOption(IPickTarget* pickTarget) override
    {
        pickTarget->EnableGraphicsElementShape();
        return true;
    }
};
```

#### 扩展能力
- **元素类型过滤**: 可基于元素类别、类型进行过滤
- **属性过滤**: 可基于元素属性值进行过滤
- **状态过滤**: 可基于元素状态（如可见性、锁定状态）进行过滤
- **层级过滤**: 可基于元素所在图层进行过滤

### 2. 拾取目标模块 (IPickTarget)

#### 功能概述
拾取目标模块定义了可以被拾取的几何类型，包括点、线、面、边等不同的几何元素。

#### 核心接口
```cpp
class IPickTarget : IObject
{
public:
    // 目标控制方法
    virtual bool EnableAll() = 0;                    // 启用所有目标
    virtual bool DisableAll() = 0;                   // 禁用所有目标
    virtual bool EnableGraphicsElementShape() = 0;   // 启用图形元素形状
    virtual bool EnableFace() = 0;                   // 启用面拾取
    virtual bool EnableCurve() = 0;                  // 启用曲线拾取
    virtual bool EnablePoint() = 0;                  // 启用点拾取
    virtual bool EnableEdge() = 0;                   // 启用边拾取
    
    // 对应的禁用方法
    virtual bool DisableFace() = 0;
    virtual bool DisableCurve() = 0;
    virtual bool DisablePoint() = 0;
    virtual bool DisableEdge() = 0;
};
```

#### 使用场景
- **建模操作**: 通常需要拾取面和边
- **标注操作**: 通常需要拾取点和边
- **测量操作**: 可能需要拾取所有类型的几何元素
- **选择操作**: 通常拾取完整的图形元素形状

### 3. UI行为模块 (ActionUiBehavior)

#### 功能概述
UI行为模块负责管理Action执行期间的用户界面状态，包括工具栏、状态栏、高亮显示等UI元素的控制。

#### 接口定义
```cpp
class IActionUiBehavior
{
public:
    virtual void InitializeUi() = 0;        // 初始化UI
    virtual void UnInitializeUi() = 0;      // 清理UI
    virtual OwnerPtr<IActionUiBehavior> Clone() const = 0;  // 克隆行为
};
```

#### 示例实现
```cpp
class ActionUiBehavior : public gcmp::IActionUiBehavior
{
private:
    bool m_bMultiSelectMode;    // 多选模式标志
    bool m_bInitialized;        // 初始化状态

public:
    virtual void InitializeUi() override
    {
        if (m_bMultiSelectMode && !m_bInitialized)
        {
            // 显示应用/取消按钮
            IUiManager* pUiMgr = IUiManager::Get();
            IApplicationWindow* pUiMainWnd = pUiMgr->GetApplicationWindow();
            pUiMainWnd->ShowApplyOrCancelGui(
                GBMP_TR(L"按范围拾取"), 
                ID_CMD_APPLY_PickNodeReferenceAction, 
                ID_CMD_CANCEL_PickNodeReferenceAction
            );
            m_bInitialized = true;
        }
    }
    
    virtual void UnInitializeUi() override
    {
        if (m_bMultiSelectMode && m_bInitialized)
        {
            // 隐藏UI元素并清理高亮
            IApplicationWindow* pUiMainWnd = IUiManager::Get()->GetApplicationWindow();
            pUiMainWnd->HideApplyOrCancelGui();
            IHighlights::Get()->Clear();
            
            // 刷新画布
            ICanvas* pCanvas = IUiDocumentViewManager::Get()->GetCurrentUiView()->GetCanvas();
            pCanvas->Refresh();
            m_bInitialized = false;
        }
    }
};
```

#### UI行为特性
- **模式感知**: 根据单选/多选模式显示不同UI
- **状态管理**: 跟踪UI初始化状态，避免重复操作
- **资源清理**: 确保Action结束时正确清理UI资源
- **视觉反馈**: 管理高亮显示和画布刷新

### 4. 工具类模块 (PickUtil)

#### 功能概述
工具类模块提供拾取操作相关的辅助功能，如元素信息获取、描述生成等。

#### 核心功能
```cpp
class PickUtil
{
public:
    // 获取元素的描述信息
    static std::wstring GetElementDescription(IDocument* pDoc, const ElementId& elementId)
    {
        const IElement* pElement = pDoc->GetElement(elementId);
        const IElementBasicInformation* pBasicInfo = pElement->GetBasicInformation();
        
        // 获取类别信息
        const UniIdentity categoryId = pBasicInfo->GetCategoryUid();
        const ICategoryLibrary* pCategoryLib = ICategoryLibrary::Get(pDoc);
        const ICategory* pCategory = pCategoryLib->GetCategory(categoryId);
        
        std::wstring result = pCategory->GetDisplayName();
        
        // 获取类型信息
        if (const IType* pType = pBasicInfo->GetType())
        {
            const IInstanceType* pInstanceType = quick_cast<const IInstanceType>(pType);
            if (pInstanceType)
            {
                if (const IFamily* pFamily = pInstanceType->GetFamily())
                    result += L" : " + pFamily->GetBasicInformation()->GetName();
            }
            result += L" : " + pType->GetBasicInformation()->GetName();
        }
        
        return result;
    }
};
```

#### 工具功能扩展
- **元素分析**: 提供元素属性、几何信息分析
- **格式化输出**: 提供统一的信息显示格式
- **缓存机制**: 可添加信息缓存以提高性能
- **国际化支持**: 支持多语言描述信息

### 5. 拾取候选项管理模块

#### 功能概述
管理拾取操作中的候选项，包括候选项更新、预高亮、选择集管理等功能。

#### 核心功能 (在SamplePickActionBase中实现)
```cpp
class PickActionUtil
{
public:
    // 更新拾取候选项
    static void UpdateCandidatesSingleton(
        IUiView* pCurrentView,
        int screenX, int screenY,
        const Vector3d& pos,
        const IPickEvent* pPickPostProcesserEvent = nullptr,
        const IPickFilter* pPickFilter = nullptr,
        const IPickTarget* pPickTarget = nullptr,
        bool isPickingHighlightOnlyGraphicsNodeAllowed = false,
        bool selectByFaceInteriorEnabled = true,
        int pickTolerance = -1,
        bool isHoveringHighlight = true
    );
};
```

#### 候选项管理特性
- **实时更新**: 根据鼠标位置实时更新候选项
- **过滤处理**: 应用拾取过滤器进行候选项筛选
- **容差控制**: 支持拾取容差设置
- **高亮反馈**: 提供候选项的视觉反馈

## 模块协作机制

### 1. 数据流向
```mermaid
graph TD
    A[用户输入] --> B[SamplePickActionBase]
    B --> C[PickFilter过滤]
    C --> D[IPickTarget目标检查]
    D --> E[候选项生成]
    E --> F[UI反馈]
    F --> G[选择集更新]
    
    H[ActionUiBehavior] --> I[UI状态管理]
    I --> F
    
    J[PickUtil] --> K[信息格式化]
    K --> F
```

### 2. 配置机制
- **策略注入**: 通过接口注入不同的过滤策略
- **参数配置**: 支持运行时参数调整
- **行为定制**: 通过UI行为组件定制界面行为

### 3. 扩展点
- **自定义过滤器**: 实现IPickFilter接口
- **自定义UI行为**: 实现IActionUiBehavior接口
- **自定义工具方法**: 扩展PickUtil类
- **事件处理器**: 添加拾取事件的后处理器

## 设计优势

1. **模块化设计**: 各功能模块职责清晰，便于维护和扩展
2. **接口抽象**: 通过接口定义标准契约，支持多种实现
3. **配置灵活**: 支持运行时配置和行为定制
4. **可测试性**: 模块间松耦合，便于单元测试
5. **可复用性**: 通用模块可在不同场景中复用

## 总结

拾取Action的功能模块采用了清晰的分层和模块化设计，每个模块都有明确的职责和接口定义。通过过滤器、目标设置、UI行为等模块的协同工作，为用户提供了灵活、可配置的拾取交互体验。这种设计既保证了功能的完整性，又提供了良好的扩展性和可维护性。
