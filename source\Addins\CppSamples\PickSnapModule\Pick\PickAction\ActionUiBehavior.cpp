#include "ActionUiBehavior.h"
#include "IUiView.h"
#include "ICanvas.h"
#include "IUiManager.h"
#include "IHighlights.h"
#include "IApplicationWindow.h"
#include "IUiDocumentViewManager.h"

#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace gcmp;
using namespace Sample;

#define ID_CMD_APPLY_PickNodeReferenceAction    L"cmdApplyPickNodeReferenceAction"
#define ID_CMD_CANCEL_PickNodeReferenceAction   L"cmdCancelPickNodeReferenceAction"

Sample::ActionUiBehavior::ActionUiBehavior(bool bMultiSelectMode)
    : m_bInitialized(false), m_bMultiSelectMode(bMultiSelectMode)
{
}

Sample::ActionUiBehavior::~ActionUiBehavior()
{
    UnInitializeUi();
}

void Sample::ActionUiBehavior::InitializeUi()
{
    if (m_bMultiSelectMode && !m_bInitialized)
    {
        IUiManager* pUiMgr = IUiManager::Get();
        DBG_WARN_AND_RETURN_VOID_UNLESS(pUiMgr, L"pUiMgr为空！",L"GDMPLab",L"2024-03-30");
        IApplicationWindow* pUiMainWnd = pUiMgr->GetApplicationWindow();
        DBG_WARN_AND_RETURN_VOID_UNLESS(pUiMainWnd, L"pUiMainWnd为空",L"GDMPLab",L"2024-03-30");
        pUiMainWnd->ShowApplyOrCancelGui(GBMP_TR(L"按范围拾取"), ID_CMD_APPLY_PickNodeReferenceAction, ID_CMD_CANCEL_PickNodeReferenceAction);
        m_bInitialized = true;
    }
}

void Sample::ActionUiBehavior::UnInitializeUi()
{
    if (m_bMultiSelectMode && m_bInitialized)
    {
        IUiManager* pUiMgr = IUiManager::Get();
        DBG_WARN_UNLESS(pUiMgr, L"pUiMgr为空！",L"GDMPLab",L"2024-03-30");
        IApplicationWindow* pUiMainWnd = pUiMgr->GetApplicationWindow();
        if (pUiMainWnd != nullptr)
        {
            pUiMainWnd->HideApplyOrCancelGui();
            if (IHighlights::Get())
                IHighlights::Get()->Clear();
            if (IUiDocumentViewManager::Get() && IUiDocumentViewManager::Get()->GetCurrentUiView())
            {
                ICanvas *pCanvas = IUiDocumentViewManager::Get()->GetCurrentUiView()->GetCanvas();
                DBG_WARN_AND_RETURN_VOID_UNLESS(pCanvas, L"pCanvas为空",L"GDMPLab",L"2024-03-30");
                pCanvas->Refresh();
            }
        }
        m_bInitialized = false;
    }
}

OwnerPtr<IActionUiBehavior> Sample::ActionUiBehavior::Clone() const
{
    OwnerPtr<ActionUiBehavior> opActionUiBehavior = NEW_AS_OWNER_PTR(ActionUiBehavior, this->m_bMultiSelectMode);
    return TransferOwnership(opActionUiBehavior);
}


