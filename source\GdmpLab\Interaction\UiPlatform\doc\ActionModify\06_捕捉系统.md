# ActionModify 捕捉系统

## 概述

捕捉系统是 ActionModify 中用于精确定位的核心机制。它能够自动识别并捕捉到图元的特征点（如端点、中点、交点等），帮助用户实现精确的图形编辑操作。捕捉系统分为近程捕捉和远程捕捉两种模式。

## 核心接口架构

捕捉的主要API接口有：
1. ISnapContext 捕捉上下文
2. ISnapSettings 捕捉设置
3. ISnap 捕捉结果
4. ISnapCandidates 捕捉候选集


## 捕捉系统架构图

```mermaid
classDiagram
    class ISnapContext {
        +Create() OwnerPtr~ISnapContext~
        +SetDocument(doc) bool
        +SetInputPoint(point) bool
        +SetSnapPlane(plane) bool
        +SetFilterForLocalSnap(filter) bool
        +SetFilterForRemoteSnap(filter) bool
        +SetDataFromMovingElements(ids, point) bool
    }
    
    class ISnapSettings {
        +GetGlobalSnapSettings() ISnapSettings*
        +DisableAllSnaps() bool
        +SetCanSnapEndPoint(value) bool
        +SetCanSnapMidPoint(value) bool
        +SetIsRemoteSnapOff(value) bool
        +SetSnapDistance(distance) int
    }
    
    class ISnap {
        +CreatePointSnap() OwnerPtr~ISnap~
        +GetSnapType() SnapType
        +GetSnappedPoint() Vector3d
        +GetShortPrompt() wstring
        +GetLongPrompt() wstring
    }
    
    class ISnapCandidates {
        +GetCandidates() vector~ISnap*~
        +GetCurrentSnap() ISnap*
        +UpdateCurrentSnapToNext() ISnap*
    }
    
    class Snapper {
        +Snap(context) OwnerPtr~ISnapCandidates~
    }
    
    class ISnapRender {
        +Get() ISnapRender*
        +Draw(snap, position) void
        +ClearSnapRenderData() void
    }
    
    class GbmpDefaultPickFilterForLocalSnap {
        +SetExcludedElementIds(ids) void
    }
    
    class GbmpDefaultPickFilterForRemoteSnap {
        +SetExcludedElementIds(ids) void
    }
    
    ISnapContext --> ISnapSettings : uses
    Snapper --> ISnapContext : uses
    Snapper --> ISnapCandidates : creates
    ISnapCandidates --> ISnap : contains
    ISnapRender --> ISnap : renders
    ISnapContext --> GbmpDefaultPickFilterForLocalSnap : uses
    ISnapContext --> GbmpDefaultPickFilterForRemoteSnap : uses
```

## 捕捉初始化流程

### 1. 捕捉上下文创建

```cpp
void GbmpActionModify::OnLButtonDown(IUiView* pCurrentView, const Vector3d& pos)
{
    // 创建捕捉上下文
    m_oSnapContext = ISnapContext::Create();
    
    // 获取最近点用于捕捉
    GetNearestPoint(pCurrentView, m_startPt, m_nearStartPt);
    
    // 设置过滤器排除移动图元
    GbmpDefaultPickFilterForLocalSnap* pLocalFilter = 
        dynamic_cast<GbmpDefaultPickFilterForLocalSnap*>(m_oFilterForLocalSnap.get());
    if (pLocalFilter)
        pLocalFilter->SetExcludedElementIds(m_moveElementIds);
        
    GbmpDefaultPickFilterForRemoteSnap* pRemoteFilter = 
        dynamic_cast<GbmpDefaultPickFilterForRemoteSnap*>(m_oFilterForRemoteSnap.get());
    if (pRemoteFilter)
        pRemoteFilter->SetExcludedElementIds(m_moveElementIds);
    
    // 设置输入点和初始化上下文
    m_oSnapContext->SetInputPoint(pos);
    bool initResult = UiSnapUtils::InitSnapContextFromUiView(
        m_oSnapContext.get(), 
        pCurrentView, 
        PickPointExchangeData::SnapPlaneType::WorkPlane
    );
    
    // 设置过滤器
    m_oSnapContext->SetFilterForLocalSnap(GetFilterForLocalSnap());
    m_oSnapContext->SetFilterForRemoteSnap(GetFilterForRemoteSnap());
    
    // 设置移动图元数据
    m_oSnapContext->SetDataFromMovingElements(m_moveElementIds, m_nearStartPt);
}
```

### 2. 夹点捕捉特殊处理

```cpp
// 夹点操作时的捕捉优先级设置
if (m_auxElementId.IsValid())
{
    IElement* pElement = pDoc->GetElement(m_auxElementId);
    std::vector<Int64> priorityTypes;
    
    if (IElementShapeHandle* pElementShapeHandle = quick_cast<IElementShapeHandle>(pElement))
    {
        const IElementShapeHandleBehavior* pBehavior = 
            pElementShapeHandle->GetElementShapeHandleBehavior();
        const IGripPointsShapeHandleBehavior* pGripPointsShapeHandleBehavior = 
            quick_cast<IGripPointsShapeHandleBehavior>(pBehavior);
            
        if (pGripPointsShapeHandleBehavior)
            priorityTypes = pGripPointsShapeHandleBehavior->GetSnapPriorityTypes();
    }
    
    if (priorityTypes.size() > 0)
    {
        SetSnapCandidatesPreprocessor(
            NEW_AS_OWNER_PTR(GbmpSnapEndPointPriorityTypesPreprocessor, priorityTypes));
        m_oSnapContext->SetSnapCandidatesPreprocessor(GetSnapCandidatesPreprocessor());
    }
}
else
{
    // 非夹点操作时禁用远程捕捉，只启用原始线捕捉
    OwnerPtr<ISnapSettings> settings = m_oSnapContext->GetSnapSettings();
    settings->SetIsRemoteSnapOff(true);
    settings->DisableAllSnaps();
    settings->SetCanSnapOriginalLine(true);
    m_oSnapContext->SetSnapSettings(settings.get());
}
```

### 3. 捕捉平面设置

```cpp
void ComputeSnapContext(ISnapContext* snapContext, IUiView* pUiView)
{
    IUiDocument* uiDoc = pUiView->GetUiDocument();
    IDocument* pDoc = uiDoc->GetDbDocument();
    snapContext->SetDocument(pDoc);
    snapContext->SetViewerContext(pUiView->GetViewerContext());
    
    // 设置捕捉平面
    if (DEBUG_MODE(TransientWorkPlane))
    {
        // 使用临时工作平面
        OwnerPtr<IPlane> opPlane = pUiView->GetWorkPlane();
        OwnerPtr<IGraphicsPlane> opGPlane = IGraphicsPlane::CreateByDirections(
            Vector3d(0, 0, 3000), 
            opPlane->GetDirectionU(), 
            opPlane->GetDirectionV()
        );
        snapContext->SetSnapPlane(*opGPlane);
    }
    else if (DEBUG_MODE_SLOW(UserCoordinateSystemWorkPlane))
    {
        // 使用用户坐标系工作平面
        // ... 坐标系设置逻辑
        snapContext->SetSnapPlane(*opGPlane);
    }
    else
    {
        // 使用默认工作平面
        OwnerPtr<IPlane> opPlane = pUiView->GetWorkPlane();
        OwnerPtr<IGraphicsPlane> opGPlane = IGraphicsPlane::CreateByDirections(
            opPlane->GetOrigin(), 
            opPlane->GetDirectionU(), 
            opPlane->GetDirectionV()
        );
        snapContext->SetSnapPlane(*opGPlane);
    }
}
```

## 捕捉执行流程

### 1. 实时捕捉处理

```cpp
void GbmpActionModify::SnapPoint(IUiView* pCurrentView, const Vector3d& pos)
{
    // 计算工作平面交点
    Vector3d posOnWorkPlane;
    ActionBase::ProjectPositionOnWorkPlane(pCurrentView, pos, posOnWorkPlane);
    
    // 更新捕捉上下文
    ComputeSnapContext(m_oSnapContext.get(), pCurrentView);
    m_oSnapContext->SetInputPoint(pos);
    
    // 特殊处理：共线捕捉
    ElementId moveElementId;
    if (!m_moveElementIds.empty())
        moveElementId = m_moveElementIds[0];
        
    if (DEBUG_MODE(CollinearSnap) && m_auxElementId != moveElementId && !m_moveElementIds.empty())
    {
        // 设置共线捕捉
        OwnerPtr<ISnapSettings> opSnapSetting = m_oSnapContext->GetSnapSettings();
        opSnapSetting->SetIsRemoteSnapOff(false);
        opSnapSetting->SetCanCentersSnap(false);
        opSnapSetting->SetCanExtensionSnap(false);
        opSnapSetting->SetCanParallelSnap(false);
        opSnapSetting->SetCanPerpendicularSnap(false);
        opSnapSetting->SetCanSnapCollineation(true);
        m_oSnapContext->SetSnapSettings(opSnapSetting.get());
        
        // 设置共线曲线
        if (!m_moveElementIds.empty())
        {
            std::vector<OwnerPtr<ICurve3d>> opComponentCurve = 
                GetElementComponentCurve(pDoc, pCurrentView, m_moveElementIds);
            if (!opComponentCurve.empty())
            {
                OwnerPtr<ICurve3d> opCollinear = opComponentCurve[0]->Clone();
                OwnerPtr<ICurve3d> opCollinearCurve = TransferOwnershipCast<ICurve3d>(
                    TransferOwnership(opCollinear));
                if (opCollinearCurve.get())
                    m_oSnapContext->SetCollinearCurve(opCollinearCurve.get());
            }
        }
    }
    
    // 执行捕捉
    m_oSnapCandidates = Snapper::Snap(m_oSnapContext.get());
    
    // 处理捕捉结果
    const ISnap* snap = m_oSnapCandidates->GetCurrentSnap();
    if (snap)
    {
        m_endPt = snap->GetSnappedPoint();
        m_isCaught = true;
        SetPromptMessage(snap->GetLongPrompt());
    }
    else
    {
        m_endPt = posOnWorkPlane;
        m_isCaught = false;
        SetPromptMessage(L"");
    }
    
    // 渲染捕捉结果
    ISnapRender::Get()->Draw(snap, SnapTextPositionType::LowerRight);
    UpdateView();
}
```

### 2. 捕捉候选切换

```cpp
bool GbmpActionModify::OnKeyDown(IUiView* pCurrentView, UINT nChar, UINT nRepCnt, UINT nFlags)
{
    // Tab键切换捕捉候选
    if (nChar == 9)  // Tab键
    {
        if (nullptr == m_oSnapCandidates)
            return false;
            
        const std::vector<const ISnap*>& snapResults = m_oSnapCandidates->GetCandidates();
        size_t numResults = snapResults.size();
        
        if (numResults > 1)
        {
            // 切换到下一个捕捉候选
            const ISnap* pSnap = m_oSnapCandidates->UpdateCurrentSnapToNext();
            
            m_isCaught = true;
            SetPromptMessage(pSnap->GetLongPrompt());
            m_endPt = pSnap->GetSnappedPoint();
            
            // 重新渲染
            ISnapRender::Get()->Draw(pSnap, SnapTextPositionType::LowerRight);
            OnMovePoint(pCurrentView);
            
            return true;
        }
    }
    
    return GbmpPickActionBase::OnKeyDown(pCurrentView, nChar, nRepCnt, nFlags);
}
```

## 近程捕捉与远程捕捉

### 1. 近程捕捉

近程捕捉用于捕捉鼠标附近的图元特征点：

```cpp
// 近程捕捉过滤器
m_oFilterForLocalSnap = NEW_AS_OWNER_PTR(GbmpDefaultPickFilterForLocalSnap, pDoc, m_moveElementIds);

// 近程捕捉设置
virtual bool CanSnapEndPoint() const = 0;      // 端点捕捉
virtual bool CanSnapMidPoint() const = 0;      // 中点捕捉
virtual bool CanSnapCenters() const = 0;       // 圆心捕捉
virtual bool CanSnapNearest() const = 0;       // 最近点捕捉
virtual bool CanSnapIntersection() const = 0;  // 交点捕捉
virtual bool CanSnapQuadrant() const = 0;      // 象限点捕捉
```

### 2. 远程捕捉

远程捕捉用于捕捉延伸线、平行线、垂直线等：

```cpp
// 远程捕捉过滤器
m_oFilterForRemoteSnap = NEW_AS_OWNER_PTR(GbmpDefaultPickFilterForRemoteSnap, pDoc, m_moveElementIds);

// 远程捕捉设置
virtual bool CanExtensionSnap() const = 0;     // 延伸线捕捉
virtual bool CanParallelSnap() const = 0;      // 平行线捕捉
virtual bool CanPerpendicularSnap() const = 0; // 垂直线捕捉
virtual bool CanSnapCollineation() const = 0;  // 共线捕捉
```

### 3. 捕捉距离设置

```cpp
// 统一捕捉距离（同时影响近程和远程）
virtual int GetSnapDistance() const = 0;
virtual bool SetSnapDistance(int snapDistance) = 0;

// 近程捕捉距离
virtual int GetSnapDistanceForLocalSnap() const = 0;
virtual bool SetSnapDistanceForLocalSnap(int snapDistance) = 0;

// 远程捕捉距离
virtual int GetSnapDistanceForRemoteSnap() const = 0;
virtual bool SetSnapDistanceForRemoteSnap(int snapDistance) = 0;
```

## 最近点计算

### 1. 基础最近点计算

```cpp
void GbmpActionModify::GetNearestPoint(IUiView* pCurrentView, 
                                      const Vector3d& inputPoint, 
                                      Vector3d& nearPoint)
{
    // 如果是辅助对象（夹点），直接获取其最近点
    if (m_auxElementId.IsValid())
    {
        IDocument* pDoc = pCurrentView->GetUiDocument()->GetDbDocument();
        IElement* pElement = pDoc->GetElement(m_auxElementId);
        IElementShapeHandle* pShapeHandle = quick_cast<IElementShapeHandle>(pElement);
        
        if (pShapeHandle)
        {
            pShapeHandle->GetElementShapeHandleBehavior()->GetNearestPoint(nearPoint);
            return;
        }
    }
    
    // 创建临时捕捉上下文进行最近点计算
    IDocument* pDoc = pCurrentView->GetUiDocument()->GetDbDocument();
    OwnerPtr<ISnapContext> opContext = ISnapContext::Create();
    ComputeSnapContext(opContext.get(), pCurrentView);
    opContext->SetInputPoint(inputPoint);
    
    // 设置只启用最近点捕捉
    OwnerPtr<ISnapSettings> settings = opContext->GetSnapSettings();
    settings->SetIsRemoteSnapOff(true);
    settings->DisableAllSnaps();
    settings->SetCanSnapNearest(true);
    opContext->SetSnapSettings(settings.get());
    
    // 执行捕捉
    OwnerPtr<ISnapCandidates> candidates = Snapper::Snap(opContext.get());
    const ISnap* pSnap = candidates->GetCurrentSnap();
    if (pSnap)
    {
        nearPoint = pSnap->GetSnappedPoint();
        // ... 其他处理逻辑
    }
}
```

## 捕捉渲染

### 1. 捕捉结果渲染

```cpp
// 渲染当前捕捉结果
ISnapRender::Get()->Draw(snap, SnapTextPositionType::LowerRight);

// 清除捕捉渲染数据
ISnapRender::Get()->ClearSnapRenderData();
```

### 2. 捕捉提示信息

```cpp
// 获取捕捉提示信息
std::wstring shortPrompt = snap->GetShortPrompt();  // 短提示
std::wstring longPrompt = snap->GetLongPrompt();    // 长提示

// 设置状态栏提示
SetPromptMessage(snap->GetLongPrompt());
```

## 调试支持

### 1. 捕捉调试模式

```cpp
// 共线捕捉调试模式
CREATE_DEBUG_MODE(CollinearSnap, L"开启共线捕捉", 
    gcmp::DebugModeGroup::DMGT_OTHERS, L"GDMPLab", L"2023-12-20", 
    L"希望开启共线捕捉，可以打开此开关！");

// 检查共线捕捉模式
if (DEBUG_MODE(CollinearSnap) && m_auxElementId != moveElementId)
{
    // 启用共线捕捉逻辑
    // ...
}
```

### 2. 捕捉状态监控

```cpp
// 检查是否捕捉到点
bool IsCaught() const { return m_isCaught; }

// 获取捕捉到的点
Vector3d GetSnappedPoint() const { return m_endPt; }

// 获取最近起始点
Vector3d GetNearStartPoint() const { return m_nearStartPt; }
```
