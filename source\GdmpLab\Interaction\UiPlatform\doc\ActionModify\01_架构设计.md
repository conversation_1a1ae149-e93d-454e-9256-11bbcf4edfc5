# ActionModify 架构设计

## 概述

ActionModify 是 GDMPLab 中负责图元修改操作的核心交互组件，基于 GCMP SDK 的 ActionBase 基类构建，采用了多层架构设计，通过继承、组合和策略模式实现了高度的可扩展性和可定制性。

## 类继承关系

```mermaid
classDiagram
    class ActionBase {
        +InitAction(view)
        +OnLButtonDown(view, pos) bool
        +OnLButtonUp(view, pos) bool
        +OnMovePoint(view, pos) bool
        +OnKeyDown(view, nChar) bool
        +OnRButtonDown(view, pos) bool
        +OnRButtonUp(view, pos) bool
        +OnLButtonDoubleClick(view, pos) bool
        +PrepareContextMenu(view) OwnerPtr~IMenuItemContainerDefinition~
        +GetPromptMessage() wstring
        +SetPromptMessage(message)
        +OnChildActionFinished(view, output)
        +StartChildAction(action)
        +MarkFinishStatus(status)
    }

    class GbmpPickActionBase {
        +UpdateCandidates()
        +OnSelectionChanged()
        +AddToSelection()
        +ClearSelection()
        -m_status
        -m_pickTarget : OwnerPtr~IPickTarget~
        -m_upPickFilter : OwnerPtr~IPickFilter~
    }

    class GbmpActionModify {
        +OnRButtonDown(view, pos) bool
        +OnRButtonUp(view, pos) bool
        +OnLButtonDoubleClick(view, pos) bool
        +PrepareContextMenu(view) OwnerPtr~IMenuItemContainerDefinition~
        +SetModifyElementsBehavior(behavior)
        -m_opModifyElementsBehavior : OwnerPtr~IActionModifyBehavior~
        -m_oSnapContext : OwnerPtr~ISnapContext~
    }

    ActionBase <|-- GbmpPickActionBase
    GbmpPickActionBase <|-- GbmpActionModify
```

## 核心组件关系

```mermaid
classDiagram
    class GbmpActionModify {
        -m_opModifyElementsBehavior : OwnerPtr~IActionModifyBehavior~
        -m_upUserTransaction : OwnerPtr~IUserTransaction~
        -m_oFilterForLocalSnap : OwnerPtr~IPickFilter~
        -m_oFilterForRemoteSnap : OwnerPtr~IPickFilter~
        -m_moveElementIds : vector~ElementId~
        +SetModifyElementsBehavior(behavior)
        +OnRButtonDown(view, pos) bool
        +OnRButtonUp(view, pos) bool
        +OnLButtonDoubleClick(view, pos) bool
        +PrepareContextMenu(view) OwnerPtr~IMenuItemContainerDefinition~
        +CollectMoveElements(doc)
        +Reset()
    }

    class IActionModifyBehavior {
        <<interface>>
        +CreateElementShadow(view, elementId) IElement*
        +ModifyElement(view, elementId, origin, moveVector) bool
        +ModifyGripPoint(view, elementId, origin, moveVector) bool
        +PrepareContextMenu(view) OwnerPtr~IMenuItemContainerDefinition~
        +OnLButtonDoubleClick(view, pos, elementId) bool
        +ProcessPostModify(view, elementIds, snapCandidates)
    }

    class ActionModifyDefaultBehavior {
        +CreateElementShadow(view, elementId) IElement*
        +ModifyElement(view, elementId, origin, moveVector) bool
        +ModifyGripPoint(view, elementId, origin, moveVector) bool
        +PrepareContextMenu(view) OwnerPtr~IMenuItemContainerDefinition~
        +OnLButtonDoubleClick(view, pos, elementId) bool
        +ProcessPostModify(view, elementIds, snapCandidates)
    }

    class GbmpModifyElementsBehavior {
        +CreateElementShadow(view, elementId) IElement*
        +ModifyElement(view, elementId, origin, moveVector) bool
        +ModifyGripPoint(view, elementId, origin, moveVector) bool
        +PrepareContextMenu(view) OwnerPtr~IMenuItemContainerDefinition~
        +OnLButtonDoubleClick(view, pos, elementId) bool
        +ProcessPostModify(view, elementIds, snapCandidates)
    }

    class GbmpMoveUtils {
        <<utility>>
        +MoveElements(doc, elementIds, origin, moveVector, modelView)
        +MoveElement(element, origin, moveVector, modelView)
        +MoveElementsForcely(doc, elementIds, moveVector, modelView)
        +MoveElementWithJoining(element, origin, moveVector, modelView, error)
    }

    class IUserTransaction {
        <<interface>>
        +Start() bool
        +Commit() bool
        +Rollback() bool
        +IsStarted() bool
    }

    class ModifyActionPickFilter {
        +AllowElement(elementId) bool
        +AllowGraphicsNode(nodeRef) bool
        +SetPickTargetOption(target) bool
    }

    class GbmpDefaultPickFilterForLocalSnap {
        +AllowElement(elementId) bool
        +AllowGraphicsNode(nodeRef) bool
        +SetExcludedElementIds(elementIds)
    }

    class GbmpDefaultPickFilterForRemoteSnap {
        +AllowElement(elementId) bool
    }

    GbmpActionModify --> IActionModifyBehavior : uses
    GbmpActionModify --> IUserTransaction : uses
    GbmpActionModify --> ModifyActionPickFilter : uses
    GbmpActionModify --> GbmpDefaultPickFilterForLocalSnap : uses
    GbmpActionModify --> GbmpDefaultPickFilterForRemoteSnap : uses

    IActionModifyBehavior <|.. ActionModifyDefaultBehavior
    ActionModifyDefaultBehavior <|-- GbmpModifyElementsBehavior

    GbmpModifyElementsBehavior --> GbmpMoveUtils : uses
    GbmpDefaultPickFilterForRemoteSnap --|> GbmpDefaultPickFilterForLocalSnap
```

## 核心接口设计

### ActionBase 基类

ActionBase 是 GCMP SDK 提供的所有 Action 的基类，定义了 Action 的基本生命周期和事件处理接口：

```cpp
class ActionBase : public IAction {
public:
    // 生命周期管理
    virtual void InitAction(IUiView* pCurrentView);
    virtual void ActionCancelled();
    virtual void GetActionOutput(ActionOutput& param) const;

    // 鼠标事件处理
    virtual bool OnLButtonDown(IUiView* pCurrentView, const Vector3d& pos);
    virtual bool OnLButtonUp(IUiView* pCurrentView, const Vector3d& pos);
    virtual bool OnLButtonDoubleClick(IUiView* pCurrentView, const Vector3d& pos);
    virtual bool OnRButtonDown(IUiView* pCurrentView, const Vector3d& pos);
    virtual bool OnRButtonUp(IUiView* pCurrentView, const Vector3d& pos);
    virtual bool OnMovePoint(IUiView* pCurrentView, const Vector3d& pos);

    // 键盘事件处理
    virtual bool OnKeyDown(IUiView* pCurrentView, int nChar);
    virtual bool OnKeyUp(IUiView* pCurrentView, int nChar);

    // 命令和视图处理
    virtual bool OnCommand(IUiView* pCurrentView, const std::wstring& idString, const CommandParameters& cmdParams);
    virtual bool OnViewSwitched(IUiView* pUIView);
    virtual void OnChildActionFinished(IUiView* pCurrentView, const ActionOutput& childActionReturnParam);

    // UI 相关
    virtual OwnerPtr<IMenuItemContainerDefinition> PrepareContextMenu(IUiView* pUIView);
    virtual std::wstring GetPromptMessage() const;
    virtual std::wstring GetMouseTooltipMessage() const;
    virtual std::wstring GetCursorPath() const;
};
```

### GbmpPickActionBase 拾取基类

GbmpPickActionBase 继承自 ActionBase，为需要拾取功能的 Action 提供基础拾取能力：

```cpp
class GbmpPickActionBase : public ActionBase {
public:
    // 重载基类方法
    virtual void InitAction(IUiView* pCurrentView) override;
    virtual bool OnLButtonDown(IUiView* pCurrentView, const Vector3d& pos) override;
    virtual bool OnLButtonUp(IUiView* pCurrentView, const Vector3d& pos) override;
    virtual bool OnKeyDown(IUiView* pCurrentView, int nChar) override;
    virtual bool OnMovePoint(IUiView* pCurrentView, const Vector3d& pos) override;
    virtual bool OnLButtonDoubleClick(IUiView* pCurrentView, const Vector3d& pos) override;
    virtual std::wstring GetPromptMessage() const override;
    virtual void ActionCancelled() override;

protected:
    // 拾取相关核心方法
    bool UpdateCandidates(IUiView* pCurrentView, const Vector3d& pos);
    bool IsAuxiliaryElement(const IDocument* pDoc, const ElementId& id);

    // 选择集管理
    virtual void OnSelectionChanged();
    virtual void AddToSelection(IDocument* pDocument, const IGraphicsNodeReference& pickResult, const IUiView* pCurrentView = nullptr);
    virtual void AddToSelectionGroup(IDocument* pDoc, GraphicsNodeReferenceOwnerPtrSet& pickResults);
    virtual void ClearSelection(IDocument* pDoc);
    virtual bool IsSelectionEmpty();

protected:
    // 核心成员变量
    unsigned int m_status;                              // 状态标记
    bool m_isCADRectSelectionFlag;                      // 矩形选择标记
    Vector3d m_startPt;                                 // 起始点
    ElementId m_auxElementId;                           // 辅助对象ID
    int m_pickPixelTolerance;                           // 拾取像素容差
    OwnerPtr<IPickTarget> m_pickTarget;                 // 拾取目标
    OwnerPtr<IPickFilter> m_upPickFilter;               // 拾取过滤器
    OwnerPtr<IUserTransaction> m_upUserTransaction;     // 用户事务
};
```

### GbmpActionModify 修改操作类

GbmpActionModify 继承自 GbmpPickActionBase，专门用于处理图元修改操作：

```cpp
class GbmpActionModify : public GbmpPickActionBase {
public:
    GbmpActionModify();
    virtual ~GbmpActionModify();

    // 重载基类方法
    virtual void InitAction(IUiView* pCurrentView) override;
    virtual void ActionCancelled() override;

    // 鼠标事件处理
    virtual bool OnRButtonDown(IUiView* pCurrentView, const Vector3d& pos) override;
    virtual bool OnRButtonUp(IUiView* pCurrentView, const Vector3d& pos) override;
    virtual bool OnLButtonDown(IUiView* pCurrentView, const Vector3d& pos) override;
    virtual bool OnLButtonUp(IUiView* pCurrentView, const Vector3d& pos) override;
    virtual bool OnLButtonDoubleClick(IUiView* pCurrentView, const Vector3d& pos) override;
    virtual bool OnMovePoint(IUiView* pCurrentView, const Vector3d& pos) override;
    virtual bool OnKeyUp(IUiView* pCurrentView, int nChar) override;

    // 上下文菜单
    virtual OwnerPtr<IMenuItemContainerDefinition> PrepareContextMenu(IUiView* pUIView) override;

public:
    // 捕捉过滤器管理
    IPickFilter* GetFilterForLocalSnap() { return m_oFilterForLocalSnap.get(); }
    void SetFilterForLocalSnap(OwnerPtr<IPickFilter> oFilterForLocalSnap);
    IPickFilter* GetFilterForRemoteSnap() { return m_oFilterForRemoteSnap.get(); }
    void SetFilterForRemoteSnap(OwnerPtr<IPickFilter> oFilterForRemoteSnap);

    // 事件处理器管理
    bool AddPickPostProcessEventHandler(OwnerPtr<IPickEventHandler> opEventHandler);
    bool AddRectPickPostProcessEventHandler(OwnerPtr<IPickEventHandler> opEventHandler);

    // 修改行为设置
    void SetModifyElementsBehavior(OwnerPtr<IActionModifyBehavior> opModifyBehavior);

protected:
    // 重载父类方法
    virtual void OnSelectionChanged() override;

private:
    // 核心私有方法
    void CollectMoveElements(IDocument* pDoc);
    void SnapPoint(IUiView* pCurrentView, const Vector3d& pos);
    void GetNearestPoint(IUiView* pCurrentView, const Vector3d& inputPoint, Vector3d& nearPoint);
    bool IsCaught() const { return m_isCaught; }
    bool HandledByOtherAction(IDocument* pDoc, const Vector3d& pos);
    bool IsElementAlreadyInSelection(const IGraphicsNodeReference* pGraphicsNodeReference);
    bool IsMoving() const;
    bool OnMovePoint(IUiView* pCurrentView);
    void Reset();

    // 影子对象管理
    IElement* GetShadow(IUiView* pCurrentView, const IElement* pElement);
    bool ClearShadows();

private:
    // 核心成员变量
    bool m_isCaught;                                    // 捕获标记
    Vector3d m_nearStartPt;                             // 最近起点
    Vector3d m_interPt;                                 // 交点
    Vector3d m_endPt;                                   // 终点
    Vector3d m_collinearVec;                            // 共线向量

    // 移动相关
    std::vector<ElementId> m_moveElementIds;            // 待移动的图元ID列表
    typedef std::map<ElementId, ElementId> ElementToShadowMap;
    ElementToShadowMap m_elementShadows;                // 图元到影子对象的映射

    // 捕捉和过滤
    OwnerPtr<ISnapContext> m_oSnapContext;              // 捕捉上下文
    OwnerPtr<IPickFilter> m_oFilterForLocalSnap;       // 近程捕捉过滤器
    OwnerPtr<IPickFilter> m_oFilterForRemoteSnap;      // 远程捕捉过滤器
    OwnerPtr<ISnapCandidates> m_oSnapCandidates;        // 捕捉候选对象

    // 事件处理
    std::vector<OwnerPtr<IPickEventHandler>> m_opPickEventHandlers;         // 点选事件处理器
    std::vector<OwnerPtr<IPickEventHandler>> m_opRectPickEventHandlers;     // 矩形选择事件处理器

    // 修改行为
    OwnerPtr<IActionModifyBehavior> m_opModifyElementsBehavior;             // 修改行为实现
};
```

### 核心方法说明

#### 1. 初始化和生命周期管理

- **InitAction()**: 初始化修改操作，设置拾取目标、过滤器等
- **ActionCancelled()**: 取消操作时的清理工作，包括回滚事务、清理选择集等
- **Reset()**: 重置内部状态，清理临时数据

#### 2. 鼠标事件处理

- **OnLButtonDown()**: 开始拾取或开始移动操作
- **OnLButtonUp()**: 完成移动操作，提交修改
- **OnMovePoint()**: 处理鼠标移动，实时更新图元位置
- **OnRButtonDown/Up()**: 处理右键菜单相关操作

#### 3. 选择集和移动管理

- **OnSelectionChanged()**: 响应选择集变化，收集待移动的图元
- **CollectMoveElements()**: 收集需要移动的图元ID列表
- **IsMoving()**: 判断当前是否处于移动状态

#### 4. 捕捉和定位

- **SnapPoint()**: 执行点捕捉操作
- **GetNearestPoint()**: 获取最近点用于精确定位
- **IsCaught()**: 判断是否捕获到有效的捕捉点

#### 5. 影子对象管理

- **GetShadow()**: 获取图元的影子对象用于拖拽预览
- **ClearShadows()**: 清理所有影子对象

#### 6. 事务管理

- **m_upUserTransaction**: 用户事务对象，管理修改操作的撤销和重做
- **事务流程**: Start() → 执行修改 → Commit() 或 Rollback()

#### 7. 过滤器管理

- **m_oFilterForLocalSnap**: 近程捕捉过滤器，过滤当前移动图元
- **m_oFilterForRemoteSnap**: 远程捕捉过滤器，过滤其他图元
- **SetFilterForLocalSnap/RemoteSnap()**: 设置自定义过滤器

## 生命周期管理

### 1. 析构函数和清理机制

```cpp
// GbmpActionModify 析构函数
GbmpActionModify::~GbmpActionModify()
{
    Reset();  // 调用 Reset() 进行清理
}

// GbmpPickActionBase 析构函数
GbmpPickActionBase::~GbmpPickActionBase()
{
    // 清理调试绘制图元
    if (m_pDebugDrawElement)
    {
        IDocument *pDoc = m_pDebugDrawElement->GetDocument();
        pDoc->DeleteElement(m_pDebugDrawElement->GetElementId());
    }

    // 清理矩形选择图元
    if (m_rectangleSelectID.IsValid())
    {
        IDocument* pDoc = GetDoc();
        if (pDoc)
        {
            pDoc->DeleteElement(m_rectangleSelectID);
            m_rectangleSelectID = ElementId::InvalidID;
        }
    }
}
```

### 2. ActionCancelled 与 Reset 的关系

```cpp
void GbmpActionModify::ActionCancelled()
{
    // 1. 调用基类处理
    GbmpPickActionBase::ActionCancelled();

    // 2. 回滚事务
    if (m_upUserTransaction != nullptr && m_upUserTransaction->IsStarted())
    {
        IDocument* pDoc = GetDoc();
        m_upUserTransaction->Rollback();
        ISelection::Get()->Clear(pDoc);
        IHighlights::Get()->Clear();
        OnSelectionChanged();
        IPickCandidates::Get()->Clear();
    }

    // 3. 清理临时图形
    CleanupTempGraphicsShape();

    // 4. 重置状态
    Reset();

    // 5. 标记完成状态
    MarkFinishStatus(ActionFinishStatus::Cancelled);
}

void GbmpActionModify::Reset()
{
    // 重置基础状态
    m_startPt = m_endPt = m_interPt = m_nearStartPt = Vector3d(0, 0, 0);
    m_isCaught = false;
    m_status = PS_GBMP_NOTSTART;

    // 清理移动图元列表
    m_moveElementIds.clear();

    // 清理影子对象
    ClearShadows();

    // 清理捕捉渲染数据
    ISnapRender::Get()->ClearSnapRenderData();
}
```

### 3. 调用关系说明

- **析构函数**: 对象销毁时自动调用，确保资源清理
- **ActionCancelled()**: 用户取消操作时调用，包含事务回滚和状态重置
- **Reset()**: 重置内部状态，被析构函数和 ActionCancelled 调用
- **调用顺序**: ActionCancelled() → Reset() 或 析构函数 → Reset()

## 工具类和辅助组件

ActionModify 依赖多个工具类和辅助组件来完成复杂的修改操作：

### 1. GbmpMoveUtils 移动工具类

负责执行实际的图元移动操作：

- **MoveElements()**: 移动多个图元，考虑连接关系和约束
- **MoveElement()**: 移动单个图元
- **MoveElementsForcely()**: 强制移动，不考虑连接关系
- **MoveElementWithJoining()**: 带连接处理的移动，处理墙体连接等

### 2. 拾取过滤器组件

- **ModifyActionPickFilter**: 修改操作专用过滤器
- **GbmpDefaultPickFilterForLocalSnap**: 近程捕捉默认过滤器，过滤当前移动的图元
- **GbmpDefaultPickFilterForRemoteSnap**: 远程捕捉默认过滤器，继承自近程过滤器并添加额外限制

### 3. 事务管理

- **IUserTransaction**: 用户事务接口，支持撤销/重做功能
- **事务生命周期**: Create → Start → 执行修改 → Commit/Rollback

### 4. 特殊图元处理

- **DrawingTable**: 绘图表格的特殊双击处理逻辑
- **IElementShapeHandle**: 辅助对象（夹点）的识别和处理
- **辅助对象过滤**: 防止辅助对象被误选或重复处理

### 5. GbmpPickActionUtil 拾取工具

提供静态拾取工具方法：

- **UpdateCandidatesSingleton()**: 执行拾取操作并更新选择集
- **IS_CAD_STYLE_SELECT**: CAD风格选择模式控制标志

详细的工具类和辅助组件说明请参考 [11_工具类和辅助组件.md](11_工具类和辅助组件.md)。

## 扩展机制

### 1. Behavior 扩展

通过实现 `IActionModifyBehavior` 接口，可以定制修改操作的具体行为：

```cpp
class CustomModifyBehavior : public IActionModifyBehavior {
public:
    virtual bool ModifyElement(const IUiView* pCurrentView,
                              const ElementId& modifiedElementId,
                              const Vector3d& originPt,
                              const Vector3d& moveVector) override {
        // 自定义修改逻辑
        return true;
    }

    virtual bool ModifyGripPoint(IUiView* pCurrentView,
                                const ElementId& modifiedElementId,
                                const Vector3d& originPt,
                                const Vector3d& moveVector) override {
        // 自定义夹点修改逻辑
        return true;
    }

    virtual IElement* CreateElementShadow(IUiView* pCurrentView,
                                         const ElementId& modifiedElementId) override {
        // 自定义影子对象创建逻辑
        return nullptr;
    }
};
```

### 2. 拾取和捕捉扩展

ActionModify 支持通过多种接口扩展拾取和捕捉行为：

#### IPickEventHandler - 拾取事件处理

通过添加事件处理器，可以在拾取过程中插入自定义逻辑：

```cpp
class CustomPickEventHandler : public IPickEventHandler {
public:
    virtual void On(IPickEventArgs* pArgs) override {
        // 获取拾取结果
        const IPickResult& pickResult = pArgs->GetPickCandidates();

        // 自定义拾取后处理逻辑
        // 例如：过滤特定类型的图元、修改拾取优先级等

        // 更新拾取结果
        pArgs->SetPickCandidates(modifiedPickResult);
    }
};

// 注册事件处理器
actionModify->AddPickPostProcessEventHandler(
    OwnerPtr<CustomPickEventHandler>::Create());
```

#### IPickFilter - 拾取过滤器

通过自定义过滤器，可以控制哪些图元可以被拾取：

```cpp
class CustomPickFilter : public IPickFilter {
public:
    virtual bool AllowElement(const ElementId& elementId) const override {
        // 自定义过滤逻辑：例如只允许拾取特定类型的图元
        return true;
    }

    virtual bool AllowGraphicsNode(const IGraphicsNodeReference& graphicsNodeReference) const override {
        // 自定义图形节点过滤逻辑
        return true;
    }

    virtual bool SetPickTargetOption(IPickTarget* pickTarget) override {
        // 配置拾取目标选项
        return true;
    }
};

// 设置过滤器
actionModify->SetFilterForLocalSnap(OwnerPtr<CustomPickFilter>::Create());
actionModify->SetFilterForRemoteSnap(OwnerPtr<CustomPickFilter>::Create());
```

#### ISnapContext - 捕捉上下文

通过捕捉上下文可以控制捕捉行为：

```cpp
// 创建和配置捕捉上下文
OwnerPtr<ISnapContext> snapContext = ISnapContext::Create();
snapContext->SetDocument(document);
snapContext->SetSnapPlane(snapPlane);
snapContext->SetFilterForLocalSnap(localSnapFilter);
snapContext->SetFilterForRemoteSnap(remoteSnapFilter);

// 应用到ActionModify
actionModify->SetSnapContext(TransferOwnership(snapContext));
```

## ActionModify 创建和配置

### 1. ActionModifyCreator 工厂类

基于 GCMP SDK 的 ActionModifyCreator 提供标准的创建方式：

```cpp
// 使用 ActionModifyCreator 创建 ActionModify 实例
ActionModifyInput modifyInput;
// 配置输入参数...
OwnerPtr<IAction> action = ActionModifyCreator::Create(modifyInput);
```

### 2. ActionModifyInput 配置类

ActionModifyInput 提供了核心的配置选项：

```cpp
class ActionModifyInput {
public:
    /// \brief 设置修改行为
    void SetActionModifyBehavior(OwnerPtr<IActionModifyBehavior> behavior);

    /// \brief 设置拾取过滤器（可选）
    void SetPickFilter(OwnerPtr<IPickFilter> filter);

    /// \brief 添加拾取后处理事件处理器（可选）
    void AddPickPostProcessEventHandler(OwnerPtr<IPickEventHandler> handler);
};

// 使用示例
ActionModifyInput input;
input.SetActionModifyBehavior(OwnerPtr<GbmpModifyElementsBehavior>::Create());
OwnerPtr<IAction> action = ActionModifyCreator::Create(input);
```

## 架构优势

1. **高度可扩展**: 通过 IActionModifyBehavior 接口，支持灵活的修改行为定制
2. **职责分离**: 修改逻辑与交互逻辑分离，便于维护和测试
3. **可配置性**: 通过不同的 Behavior 实现，可以配置出不同的修改行为
4. **复用性**: 基础的 ActionModify 框架可以在不同场景下复用
5. **标准化**: 基于 GCMP SDK 标准接口，确保一致性和兼容性

## 设计考虑

1. **性能优化**: 优化图元修改和移动操作的性能
2. **内存管理**: 使用 OwnerPtr 进行自动内存管理，避免内存泄漏
3. **事务处理**: 支持修改操作的撤销和重做
4. **向后兼容**: 保持与旧版本的兼容性
5. **接口一致性**: 遵循 GCMP SDK 的接口设计规范
