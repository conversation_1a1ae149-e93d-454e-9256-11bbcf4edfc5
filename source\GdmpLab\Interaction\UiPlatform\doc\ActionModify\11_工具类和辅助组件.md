# ActionModify 工具类和辅助组件

## 概述

ActionModify 系统依赖多个工具类和辅助组件来完成复杂的修改操作。这些组件包括移动工具类、拾取过滤器、事务管理器等，它们为 ActionModify 提供了底层的功能支持。

## 核心工具类

### 1. GbmpMoveUtils 移动工具类

GbmpMoveUtils 是执行图元移动操作的核心工具类，提供了多种移动策略：

```cpp
class GbmpMoveUtils
{
public:
    /// \brief 移动多个图元（考虑连接关系）
    /// \param pDoc 文档指针
    /// \param moveElementIds 待移动的图元ID列表
    /// \param originPosition 原始位置
    /// \param moveVector 移动向量
    /// \param pModelView 模型视图
    static void MoveElements(IDocument* pDoc, 
                           const std::vector<ElementId>& moveElementIds, 
                           const Vector3d& originPosition, 
                           const Vector3d& moveVector, 
                           const IModelView* pModelView);

    /// \brief 移动单个图元（考虑连接关系）
    /// \param pElement 待移动的图元
    /// \param originPosition 原始位置
    /// \param moveVector 移动向量
    /// \param pModelView 模型视图
    static void MoveElement(IElement* pElement, 
                          const Vector3d& originPosition, 
                          const Vector3d& moveVector, 
                          const IModelView* pModelView);

    /// \brief 强制移动图元（不考虑连接关系）
    /// \param pDoc 文档指针
    /// \param moveElementIds 待移动的图元ID列表
    /// \param moveVector 移动向量
    /// \param pModelView 模型视图
    static void MoveElementsForcely(IDocument* pDoc, 
                                  const std::vector<ElementId>& moveElementIds, 
                                  const Vector3d& moveVector, 
                                  const IModelView* pModelView);

    /// \brief 带连接处理的图元移动
    /// \param pElement 待移动的图元
    /// \param originPosition 原始位置
    /// \param moveVector 移动向量
    /// \param pModelView 模型视图
    /// \param strError 错误信息输出
    /// \return 移动是否成功
    static bool MoveElementWithJoining(IElement* pElement, 
                                     const Vector3d& originPosition, 
                                     const Vector3d& moveVector, 
                                     const IModelView* pModelView, 
                                     std::wstring& strError);
};
```

#### 移动策略说明

1. **MoveElements()**: 标准移动方法，会考虑图元间的连接关系，自动处理连接更新
2. **MoveElementsForcely()**: 强制移动，不考虑连接关系，适用于需要精确控制的场景
3. **MoveElementWithJoining()**: 专门处理有连接关系的图元移动，如墙体连接

### 2. GbmpPickActionUtil 拾取工具类

提供拾取操作的静态工具方法：

```cpp
class GbmpPickActionUtil
{
public:
    /// \brief 在指定位置执行拾取操作
    /// \param pCurrentView 当前视图
    /// \param screenX 屏幕X坐标
    /// \param screenY 屏幕Y坐标
    /// \param pos 三维位置
    /// \param pPickPostProcesserEvent 拾取后处理事件
    /// \param pPickFilter 拾取过滤器
    /// \param pPickTarget 拾取目标
    /// \param isPickingHighlightOnlyGraphicsNodeAllowed 是否允许拾取高亮图形节点
    /// \param selectByFaceInteriorEnabled 是否启用面内部选择
    /// \param pickTolerance 拾取容差
    /// \param isHoveringHighlight 是否悬停高亮
    static void UpdateCandidatesSingleton(
        gcmp::IUiView* pCurrentView,
        int screenX, int screenY,
        const gcmp::Vector3d& pos,
        const gcmp::IPickEvent* pPickPostProcesserEvent = nullptr,
        const gcmp::IPickFilter* pPickFilter = nullptr,
        const gcmp::IPickTarget* pPickTarget = nullptr,
        bool isPickingHighlightOnlyGraphicsNodeAllowed = false,
        bool selectByFaceInteriorEnabled = true,
        int pickTolerance = -1,
        bool isHoveringHighlight = true);
        
    /// \brief CAD 风格选择标志
    /// false: 传统模式（点击空白清除选择）
    /// true:  CAD 风格模式（点击空白保持选择）
    static bool IS_CAD_STYLE_SELECT;
};
```

## 拾取过滤器组件

### 1. ModifyActionPickFilter

专门用于修改操作的拾取过滤器，控制哪些图元可以被修改操作拾取：

```cpp
class ModifyActionPickFilter : public IPickFilter
{
public:
    ModifyActionPickFilter(IDocument* pDoc);
    
    /// \brief 判断图元是否允许被拾取
    virtual bool AllowElement(const ElementId& elemId) const override;
    
    /// \brief 判断图形节点是否允许被拾取
    virtual bool AllowGraphicsNode(const IGraphicsNodeReference& nodeReference) const override;
    
    /// \brief 设置拾取目标选项
    virtual bool SetPickTargetOption(IPickTarget* pickTarget) override;
};
```

### 2. GbmpDefaultPickFilterForLocalSnap

近程捕捉的默认过滤器：

```cpp
class GbmpDefaultPickFilterForLocalSnap : public IPickFilter
{
public:
    GbmpDefaultPickFilterForLocalSnap(IDocument* pDoc, 
                                    const std::vector<ElementId>& excludedElements = {});
    
    virtual bool AllowElement(const ElementId& elemId) const override;
    virtual bool AllowGraphicsNode(const IGraphicsNodeReference& nodeReference) const override;
    virtual bool SetPickTargetOption(IPickTarget* pickTarget) override;
    
    /// \brief 设置排除的图元ID列表
    virtual void SetExcludedElementIds(const std::vector<ElementId>& elementIds);

protected:
    IDocument* m_pDoc;
    std::vector<ElementId> m_excludedElements;
};
```

### 3. GbmpDefaultPickFilterForRemoteSnap

远程捕捉的默认过滤器，继承自近程捕捉过滤器：

```cpp
class GbmpDefaultPickFilterForRemoteSnap : public GbmpDefaultPickFilterForLocalSnap
{
public:
    GbmpDefaultPickFilterForRemoteSnap(IDocument* pDoc, 
                                     const std::vector<ElementId>& excludedElements = {});
    
    /// \brief 重载以实现远程捕捉的特殊过滤逻辑
    virtual bool AllowElement(const ElementId& elemId) const override;
};
```

#### 近程与远程捕捉的区别

- **近程捕捉**: 捕捉当前正在移动的图元上的特征点
- **远程捕捉**: 捕捉其他图元上的特征点，用于精确定位
- **过滤差异**: 远程捕捉会排除某些特定类型的图元（如柱子）

## 事务管理组件

### 1. IUserTransaction 用户事务

ActionModify 使用用户事务来管理修改操作的撤销和重做：

```cpp
// 在 GbmpActionModify 中的事务使用
class GbmpActionModify {
private:
    OwnerPtr<IUserTransaction> m_upUserTransaction;  // 用户事务
    
public:
    virtual void ActionCancelled() override {
        // 取消操作时回滚事务
        if (m_upUserTransaction && m_upUserTransaction->IsStarted()) {
            m_upUserTransaction->Rollback();
        }
    }
    
private:
    bool OnLButtonUp(IUiView* pCurrentView, const Vector3d& pos) override {
        // 移动完成时提交事务
        if (m_upUserTransaction) {
            m_upUserTransaction->Start();
            // 执行修改操作...
            m_upUserTransaction->Commit();
        }
    }
};
```

### 2. 事务处理流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Action as GbmpActionModify
    participant Transaction as IUserTransaction
    participant Doc as IDocument
    
    User->>Action: 开始拖拽
    Action->>Transaction: Create("移动")
    Action->>Transaction: Start()
    
    User->>Action: 拖拽移动
    Action->>Doc: 修改图元位置
    
    User->>Action: 完成拖拽
    Action->>Transaction: Commit()
    
    Note over Transaction: 操作可撤销
    
    User->>Action: 取消操作
    Action->>Transaction: Rollback()
    Note over Doc: 恢复到原始状态
```

## 特殊图元处理组件

### 1. DrawingTable 特殊处理

ActionModify 对 DrawingTable（绘图表格）有特殊的处理逻辑：

```cpp
// 在 OnLButtonDoubleClick 中的特殊处理
bool GbmpActionModify::OnLButtonDoubleClick(IUiView* pCurrentView, const Vector3d& pos)
{
    const GraphicsNodeReferenceOwnerPtrSet& gnodeRefs = ISelection::Get()->GetGraphicsNodeReferences();
    
    // 检查是否选中了 DrawingTable
    IDrawingTable* pTableElement = nullptr;
    if (!gnodeRefs.empty()) {
        IElement* pElement = GetDoc()->GetElement((*gnodeRefs.begin())->GetElementId());
        IGenericElement* pGenericElement = quick_cast<IGenericElement>(pElement);
        if (pGenericElement) {
            pTableElement = quick_cast<IDrawingTable>(pGenericElement->GetExternalObject());
        }
    }
    
    // 对 DrawingTable 的特殊处理
    if (pTableElement) {
        // 特殊的双击处理逻辑
        // ...
    }
}
```

### 2. 辅助对象处理

对于辅助对象（如夹点），ActionModify 有专门的处理机制：

```cpp
bool GbmpActionModify::IsAuxiliaryElement(const IDocument* pDoc, const ElementId& id)
{
    IElement* pElement = pDoc->GetElement(id);
    IElementShapeHandle* pShapeHandle = quick_cast<IElementShapeHandle>(pElement);
    return pShapeHandle != nullptr;
}
```

## 组件协作关系

```mermaid
classDiagram
    class GbmpActionModify {
        -m_upUserTransaction : OwnerPtr~IUserTransaction~
        -m_oFilterForLocalSnap : OwnerPtr~IPickFilter~
        -m_oFilterForRemoteSnap : OwnerPtr~IPickFilter~
        +CollectMoveElements()
        +OnLButtonUp()
    }
    
    class GbmpMoveUtils {
        +MoveElements()
        +MoveElement()
        +MoveElementsForcely()
        +MoveElementWithJoining()
    }
    
    class GbmpPickActionUtil {
        +UpdateCandidatesSingleton()
        +IS_CAD_STYLE_SELECT : bool
    }
    
    class ModifyActionPickFilter {
        +AllowElement()
        +AllowGraphicsNode()
    }
    
    class GbmpDefaultPickFilterForLocalSnap {
        +AllowElement()
        +SetExcludedElementIds()
    }
    
    class GbmpDefaultPickFilterForRemoteSnap {
        +AllowElement()
    }
    
    class IUserTransaction {
        +Start()
        +Commit()
        +Rollback()
    }
    
    GbmpActionModify --> GbmpMoveUtils : uses
    GbmpActionModify --> GbmpPickActionUtil : uses
    GbmpActionModify --> ModifyActionPickFilter : uses
    GbmpActionModify --> GbmpDefaultPickFilterForLocalSnap : uses
    GbmpActionModify --> GbmpDefaultPickFilterForRemoteSnap : uses
    GbmpActionModify --> IUserTransaction : uses
    
    GbmpDefaultPickFilterForRemoteSnap --|> GbmpDefaultPickFilterForLocalSnap
```

## 使用示例

### 1. 配置自定义移动行为

```cpp
class CustomModifyBehavior : public ActionModifyDefaultBehavior
{
public:
    virtual bool ModifyElement(const IUiView* pCurrentView,
                              const ElementId& modifiedElementId,
                              const Vector3d& originPt,
                              const Vector3d& moveVector) override
    {
        IDocument* pDoc = pCurrentView->GetUiDocument()->GetDbDocument();
        IElement* pElement = pDoc->GetElement(modifiedElementId);
        
        // 使用 GbmpMoveUtils 执行移动
        GbmpMoveUtils::MoveElement(pElement, originPt, moveVector, 
                                 pCurrentView->GetModelView());
        return true;
    }
};
```

### 2. 配置自定义过滤器

```cpp
class CustomPickFilter : public GbmpDefaultPickFilterForLocalSnap
{
public:
    CustomPickFilter(IDocument* pDoc) : GbmpDefaultPickFilterForLocalSnap(pDoc) {}
    
    virtual bool AllowElement(const ElementId& elemId) const override
    {
        // 先调用基类过滤
        if (!GbmpDefaultPickFilterForLocalSnap::AllowElement(elemId))
            return false;
            
        // 添加自定义过滤逻辑
        IElement* pElement = m_pDoc->GetElement(elemId);
        // 例如：只允许特定类型的图元
        return IsAllowedElementType(pElement);
    }
};
```

这些工具类和辅助组件为 ActionModify 提供了强大的底层支持，使得复杂的修改操作能够高效、可靠地执行。
