# ActionModify 拾取机制

## 概述

拾取机制是 ActionModify 的基础功能，负责在用户交互过程中识别和选择图元。GDMPLab 的拾取机制支持点选、框选、过滤器、容差设置等多种功能，并通过 GDMP SDK 的拾取接口实现。

## 拾取状态枚举

```cpp
enum EnGbmpPickStatus
{
    PS_GBMP_NOTSTART = 0,        // 未开始
    PS_GBMP_LBUTTON_DOWN = 1,    // 鼠标左键按下
    PS_GBMP_LBUTTON_UP = 2,      // 鼠标左键抬起
    PS_GBMP_LBUTTON_MOVING = 4,  // 鼠标左键拖拽中
    PS_GBMP_LBUTTON_DBCLK = 8    // 鼠标左键双击
};
```

## 核心拾取工具类

### GbmpPickActionUtil

提供静态方法进行拾取操作：

```cpp
class GbmpPickActionUtil
{
public:
    // 在视图的给定屏幕位置做一次拾取操作，并更新全局的选择集
    static void UpdateCandidatesSingleton(
        gcmp::IUiView* pCurrentView,
        int screenX, int screenY,
        const gcmp::Vector3d& pos,
        const gcmp::IPickEvent* pPickPostProcesserEvent = nullptr,
        const gcmp::IPickFilter* pPickFilter = nullptr,
        const gcmp::IPickTarget* pPickTarget = nullptr,
        bool isPickingHighlightOnlyGraphicsNodeAllowed = false,
        bool selectByFaceInteriorEnabled = true,
        int pickTolerance = -1,
        bool isHoveringHighlight = true);
        
    static bool IS_CAD_STYLE_SELECT;  // CAD 风格选择标志
};
```

## 拾取流程

### 1. 点选拾取流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Action as GbmpActionModify
    participant Base as GbmpPickActionBase
    participant Util as GbmpPickActionUtil
    participant SDK as GDMP SDK
    
    User->>Action: OnLButtonDown()
    Action->>Base: UpdateCandidates()
    Base->>Util: UpdateCandidatesSingleton()
    Util->>SDK: IPicker::PickByPoint()
    SDK-->>Util: IPickResult
    Util->>SDK: IPickCandidates::Get()->AddPick()
    Util->>SDK: IPreHighlights::Get()->AddGraphicsNodeReferences()
    Base->>Action: OnSelectionChanged()
```

### 2. 拾取上下文创建

```cpp
// 创建点选拾取上下文
OwnerPtr<IPointPickContext> pickContext = gcmp::IPointPickContext::Create();

// 设置基本参数
pickContext->SetDocument(pDoc);
pickContext->SetViewerContext(pCurrentView->GetViewerContext()->Clone());
pickContext->SetGraphicsElementShapes(apiGreps);
pickContext->SetRay(ray.get());
pickContext->SetPickTolerance(tolerance);

// 设置拾取过滤器
if(pPickFilter != nullptr)
    pickContext->SetPickFilter(pPickFilter);

// 设置拾取目标
if(pPickTarget)
    pickContext->SetPickTarget(TransferOwnership(opTarget));

// 设置后处理事件
if (pPickPostProcesserEvent)
    pickContext->SetPickPostProcessEvent(pPickPostProcesserEvent);
```

### 3. 拾取结果处理

```cpp
// 执行拾取
OwnerPtr<IPickResult> pickResult = gcmp::IPicker::PickByPoint(pickContext.get());

// 清空候选项和预高亮
IPickCandidates::Get()->Clear();
IPreHighlights::Get()->Clear();

// 添加拾取结果到候选项
FOR_EACH(itIPick, pickResult->GetAllPicks())
{
    IPickCandidates::Get()->AddPick(TransferOwnership(itIPick));
}

// 设置默认当前拾取索引
IPickCandidates::Get()->SetDefaultCurrentPickIndex(pDoc);

// 添加预高亮
if (IPickCandidates::Get()->GetCount() > 0)
{
    IPreHighlights::Get()->AddGraphicsNodeReferences(
        IPickCandidates::Get()->GetCurrentPick()->GetAllGraphicsNodeReferences());
}
```

## 拾取配置

### 1. 拾取容差设置

```cpp
class GbmpPickActionBase {
private:
    int m_pickPixelTolerance;  // 拾取像素容差
    
public:
    int GetPickPixelTolerance() { return m_pickPixelTolerance; }
    void SetPickPixelTolerance(int tolerance);
};
```

### 2. 拾取过滤器

```cpp
class GbmpPickActionBase {
private:
    gcmp::OwnerPtr<gcmp::IPickFilter> m_upPickFilter;  // 拾取过滤器
    
public:
    gcmp::IPickFilter* GetPickFilter() { return m_upPickFilter.get(); }
    void SetPickFilter(OwnerPtr<gcmp::IPickFilter> opPickFilter);
};
```

### 3. 拾取目标

```cpp
class GbmpPickActionBase {
private:
    gcmp::OwnerPtr<gcmp::IPickTarget> m_pickTarget;  // 拾取目标
    
public:
    gcmp::IPickTarget* GetPickTarget() { return m_pickTarget.get(); }
    void SetPickTarget(OwnerPtr<gcmp::IPickTarget> pickTarget);
};
```

## 框选拾取

### 1. 框选矩形绘制

```cpp
// 创建选择矩形图形
OwnerPtr<IGraphicsElementShape> CreatSelectRectangleGrep(
    const IUiView* pCurrentView, 
    const Vector3d& pos) const;

// 绘制选择矩形
void DrawSelectRectangle(IUiView* pCurrentView, 
                        const Vector3d& pos, 
                        bool isIntersect);
```

### 2. 框选状态管理

```cpp
class GbmpPickActionBase {
private:
    bool m_isCADRectSelectionFlag;           // CAD 风格框选标志
    gcmp::Vector3d m_startPt;                // 框选起点
    gcmp::Vector3d m_worldStart;             // 世界坐标起点
    std::vector<gcmp::ElementId> m_selectRect; // 框选矩形
    gcmp::ElementId m_rectangleSelectID;     // 框选矩形ID
};
```

## 拾取事件处理

### 1. 拾取后处理事件

```cpp
class GbmpPickActionBase {
private:
    gcmp::OwnerPtr<gcmp::IPickEvent> m_opPickPostProcessEvent;     // 点选后处理事件
    gcmp::OwnerPtr<gcmp::IPickEvent> m_opRectPickPostProcessEvent; // 框选后处理事件
    
public:
    bool AddPickPostProcessEvent(gcmp::IPickEventHandler* pPickEvent);
    gcmp::IPickEvent* GetPickPostProcessEvent() const;
    void SetPickPostProcessEvent(OwnerPtr<gcmp::IPickEvent> opPickPostProcessEvent);
    
    bool AddRectPickPostProcessEvent(gcmp::IPickEventHandler* pPickEvent);
    gcmp::IPickEvent* GetRectPickPostProcessEvent() const;
    void SetRectPickPostProcessEvent(OwnerPtr<gcmp::IPickEvent> opRectPickPostProcessEvent);
};
```

### 2. 键盘事件处理

```cpp
bool GbmpPickActionBase::OnKeyDown(IUiView* pCurrentView, int nChar)
{
    IPickCandidates* candidates = IPickCandidates::Get();
    
    // Tab 键切换候选项
    if (nChar == 9 && candidates->GetCount() > 0)
    {
        int index = candidates->GetCurrentPickIndex();
        if (IsKeyAndButtonPressed(VK_SHIFT))
            index = (index == 0) ? (int)candidates->GetCount()-1 : index-1;
        else
            index = (index + 1) % candidates->GetCount();
            
        candidates->SetCurrentPickIndex(index);
        IPreHighlights::Get()->SetGraphicsNodeReferences(
            candidates->GetCurrentPick()->GetAllGraphicsNodeReferences());
        UpdateView();
    }
    // ESC 键取消选择
    else if (nChar == VK_ESCAPE)
    {
        ClearSelection(pCurrentView->GetUiDocument()->GetDbDocument());
        OnSelectionChanged();
        UpdateView();
    }
    // Delete 键删除选择
    else if (nChar == VK_DELETE)
    {
        ICommandManager::Get()->SendCommand(ID_CMD_GBMP_DELETE_SELECTION);
    }
}
```

## 调试模式支持

### 1. 调试模式定义

```cpp
CREATE_DEBUG_MODE(GBMPDontPrintCandidateOnStatusBar, 
    L"状态栏不显示拾取候选项", 
    gcmp::DebugModeGroup::DMGT_GREP_AND_PICKSNAP, 
    L"GDMPLab", L"2023-12-20");

CREATE_DEBUG_MODE(GBMPPickFaceInWireframeMode, 
    L"线框模式下拾取面", 
    gcmp::DebugModeGroup::DMGT_GREP_AND_PICKSNAP, 
    L"GDMPLab", L"2023-12-20");

CREATE_DEBUG_MODE(GBMPPickSegmentOfPolyCurve, 
    L"多段线分段拾取", 
    gcmp::DebugModeGroup::DMGT_GREP_AND_PICKSNAP, 
    L"GDMPLab", L"2023-12-20");
```

### 2. 调试模式应用

```cpp
// 线框模式下拾取面
if (DEBUG_MODE(GBMPPickFaceInWireframeMode))
{
    pickContext->SetSelectByFaceInteriorEnabled(true);
}

// 多段线分段拾取
if (DEBUG_MODE(GBMPPickSegmentOfPolyCurve))
{
    opTarget->EnableCurve();
    opTarget->EnableSegmentOfPolyCurve();
}
```

## 性能优化

### 1. 大场景优化

```cpp
// 大场景拾取容差设置
const double MAX_TOLERANCE_IN_LARGE_SCENE = 100.0;
pickContext->SetPickToleranceForLargeScene(MAX_TOLERANCE_IN_LARGE_SCENE);

// 大场景图形数量阈值
if (DEBUG_MODE(GrepSizeThresholdForLargeScene))
    pickContext->SetGrepSizeThresholdForLargeScene(1000);
```

### 2. 高亮优化

```cpp
// 控制是否允许拾取仅高亮可见的图形节点
void SetIsPickingHighlightOnlyGraphicsNodeAllowed(bool IsAllowed) 
{ 
    m_isPickingHighlightOnlyGraphicsNodeAllowed = IsAllowed; 
}

// 控制是否支持掠过高亮
bool m_isHoveringHighlight;
```

## 扩展机制

### 1. 自定义拾取过滤器

```cpp
class CustomPickFilter : public IPickFilter
{
public:
    virtual bool IsElementAllowed(const IElement* pElement) override
    {
        // 自定义过滤逻辑
        return true;
    }
};
```

### 2. 自定义拾取事件处理器

```cpp
class CustomPickEventHandler : public IPickEventHandler
{
public:
    virtual bool HandlePickEvent(const IPickEvent* pPickEvent) override
    {
        // 自定义拾取后处理逻辑
        return true;
    }
};
```
