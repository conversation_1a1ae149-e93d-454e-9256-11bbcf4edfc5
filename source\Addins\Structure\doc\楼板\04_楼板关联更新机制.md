## 楼板的父子关系管理机制

楼板图元在GDMP中实现了完整的父子关系管理机制，通过`ReportParents`和`UpdateForWeakParentDeletion`方法来管理与轮廓线和坡度线等图元的依赖关系。

### Strong Parent vs Weak Parent概念

GDMP中的图元依赖关系分为两种类型：

1. **Strong Parent（强父图元）**：当强父图元被删除时，当前图元也会被系统自动删除
2. **Weak Parent（弱父图元）**：当弱父图元被删除时，当前图元会收到通知，可以进行相应的处理而不被删除

### 楼板图元的父子关系实现

#### StructureFloor实现

楼板图元主要与轮廓线和坡度线建立弱引用关系：

```cpp
void StructureFloor::ReportParents(IElementParentReporter& reporter) const
{
    if(GetSlopeLineId__().IsValid())
    {
        reporter.ReportWeak(GetSlopeLineId__());
    }
    FOR_EACH(id, GetProfileIds__())
    {
        reporter.ReportWeak(id);
    }
}
```

```cpp
void StructureFloor::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
{
    FOR_EACH(id, deletedElementIds)
    {
        if(id == GetSlopeLineId__())
        {
            SetSlopeLineId__(ElementId::InvalidID);
        }
        auto it = std::find(GetProfileIdsFW__().begin(), GetProfileIdsFW__().end(), id);
        if(it != GetProfileIdsFW__().end())
        {
            GetProfileIdsFW__().erase(it);
        }
    }
}
```

### 楼板与相关图元的弱引用关系

#### ReportParents方法分析

`ReportParents`方法的实现逻辑：

1. **坡度线依赖**：如果楼板关联了坡度线，通过`reporter.ReportWeak(GetSlopeLineId__())`报告弱引用
2. **轮廓线依赖**：遍历所有轮廓线ID，通过`reporter.ReportWeak(id)`将每个轮廓线ID报告为弱父图元
3. **弱引用特性**：坡度线或轮廓线被删除时，楼板不会自动删除，而是通过`UpdateForWeakParentDeletion`处理

#### UpdateForWeakParentDeletion方法分析

`UpdateForWeakParentDeletion`方法的处理流程：

1. **遍历删除的图元ID**：检查每个被删除的图元ID
2. **处理坡度线删除**：如果删除的是关联的坡度线，将坡度线ID设置为无效
3. **处理轮廓线删除**：在楼板的轮廓线列表中查找匹配的ID，如果找到则移除该引用
4. **维护一致性**：确保楼板的引用列表与实际存在的图元保持一致

### 父子关系管理的调用流程

```mermaid
sequenceDiagram
    participant System as GDMP系统
    participant Floor as 楼板
    participant SlopeLine as 坡度线
    participant ProfileLine as 轮廓线

    Note over System: 依赖关系收集阶段
    System->>Floor: ReportParents()
    Floor->>System: ReportWeak(坡度线ID)
    Floor->>System: ReportWeak(轮廓线ID)

    Note over System: 弱父图元删除阶段
    System->>SlopeLine: 删除坡度线
    System->>ProfileLine: 删除轮廓线
    System->>Floor: UpdateForWeakParentDeletion(删除的ID集合)
    Floor->>Floor: 清理坡度线引用
    Floor->>Floor: 从轮廓线列表中移除对应ID
```

### 设计特点

楼板的父子关系管理具有以下特点：

1. **弱引用设计**：楼板对坡度线和轮廓线采用弱引用，相关图元删除时楼板不会被删除
2. **自动清理**：当相关图元被删除时，楼板会自动清理无效的引用
3. **灵活性**：支持坡度线和轮廓线的动态添加和删除，适应编辑过程中的变化

## 计算器注册
楼板通过计算器实现各种参数的自动计算和更新。在`StructureFloor::GetCalculators`方法中注册了相关计算器：

```cpp
DECLARE_CALCULATOR_CREATOR(InstanceVolumeCalculator)
DECLARE_CALCULATOR_CREATOR(StructureFloorAreaCalculator)
DECLARE_CALCULATOR_CREATOR(StructureFloorTopElevationCalculator)
DECLARE_CALCULATOR_CREATOR(StructureFloorBottomElevationCalculator)
DECLARE_CALCULATOR_CREATOR(StructureFloorSlopeAngleCalculator)
DECLARE_CALCULATOR_CREATOR(StructureFloorSlopeStartHeightCalculator)
DECLARE_CALCULATOR_CREATOR(StructureFloorSlopeEndHeightCalculator)

void StructureFloor::GetCalculators(ICalculatorCollection * calculators) const
{
    ADD_CALCULATOR(InstanceVolumeCalculator, GetDocument(), GetVolumeRdId());
    ADD_CALCULATOR(StructureFloorAreaCalculator, GetDocument(), GetAreaRdId());
    ADD_CALCULATOR(StructureFloorTopElevationCalculator, GetDocument(), GetTopElevationRdId());
    ADD_CALCULATOR(StructureFloorBottomElevationCalculator, GetDocument(), GetBottomElevationRdId());
    ADD_CALCULATOR(StructureFloorSlopeAngleCalculator, GetDocument(), GetSlopeAngleRdId());
    ADD_CALCULATOR(StructureFloorSlopeStartHeightCalculator, GetDocument(), GetSlopeStartHeightRdId());
    ADD_CALCULATOR(StructureFloorSlopeEndHeightCalculator, GetDocument(), GetSlopeEndHeightRdId());
}
```

这里注册了七个计算器，分别用于计算楼板的体积、面积、顶部标高、底部标高、坡度角度、坡度起点高度和坡度终点高度。每个计算器都与特定的RegenDataId关联，当这些数据发生变化时，会触发相应计算器的执行。

## 计算器实现

### 楼板体积计算器

`InstanceVolumeCalculator`是楼板的体积计算器，用于计算楼板的体积：

```cpp
class InstanceVolumeCalculator : public GbmpCalculatorBase
{
    DECLARE_CALCULATOR(InstanceVolumeCalculator, IInstance)
public:
    InstanceVolumeCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : GbmpCalculatorBase(pDoc, outputDataId) {}
    virtual void ReportInputDataIds(std::vector<RegenDataId> & dataIds) const override;
    virtual void Execute() override;
};
```

#### 报告输入数据依赖

`ReportInputDataIds`方法报告了计算器依赖的输入数据：

```cpp
void InstanceVolumeCalculator::ReportInputDataIds(std::vector<RegenDataId> & dataIds) const
{
    IInstance* pTarget = GetElement<IInstance>();
    const IElementModelShape* pGrepBehavior = pTarget->GetElementModelShape();
    dataIds.push_back(pGrepBehavior->GetGraphicsElementShapeRdId());
}
```

这里报告了楼板的图形元素形状ID，当楼板的形状发生变化时，会触发计算器执行。

#### 执行计算

`Execute`方法执行实际的计算：

```cpp
void InstanceVolumeCalculator::Execute()
{
    IInstance *pInstance = GetElement<IInstance>();
    double volume;
    if (StructureInstanceUtils::GetInstanceVolume(pInstance, volume))
    {
        UniIdentity paramUId = PARAMETER_UID(VolumeBuiltInParameter);
        StructureInstanceUtils::SetInstanceOutputParamValueByParamUid(pInstance, volume, paramUId);
    }
}
```

这个方法的执行流程是：

1. 获取楼板实例
2. 调用StructureInstanceUtils::GetInstanceVolume计算楼板的体积
3. 将计算结果设置到VolumeBuiltInParameter参数中

### 楼板标高计算器

楼板的标高计算通过`StructureFloorElevationCalculator`系列类实现：

```cpp
class StructureFloorElevationCalculatorBase :public GbmpCalculatorBase
{
public:
    StructureFloorElevationCalculatorBase(IDocument* pDoc, const RegenDataId& outputDataId) : GbmpCalculatorBase(pDoc, outputDataId) {}
    virtual void ReportInputDataIds(std::vector<RegenDataId> & dataIds) const override;
};

class StructureFloorTopElevationCalculator : public StructureFloorElevationCalculatorBase
{
    DECLARE_CALCULATOR(StructureFloorTopElevationCalculator, IInstance)
public:
    StructureFloorTopElevationCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : StructureFloorElevationCalculatorBase(pDoc, outputDataId) {}
    virtual void Execute() override;
};

class StructureFloorBottomElevationCalculator : public StructureFloorElevationCalculatorBase
{
    DECLARE_CALCULATOR(StructureFloorBottomElevationCalculator, IInstance)
public:
    StructureFloorBottomElevationCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : StructureFloorElevationCalculatorBase(pDoc, outputDataId) {}
    virtual void Execute() override;
};
```

#### 报告输入数据依赖

`StructureFloorElevationCalculatorBase`的`ReportInputDataIds`方法报告了计算器依赖的输入数据：

```cpp
void StructureFloorElevationCalculatorBase::ReportInputDataIds(std::vector<RegenDataId> & dataIds) const
{
    IInstance* pTarget = GetElement<IInstance>();
    const IElementModelShape* pGrepBehavior = pTarget->GetElementModelShape();
    dataIds.push_back(pGrepBehavior->GetGraphicsElementShapeRdId());
}
```

这里报告了楼板的图形元素形状ID，当楼板的形状发生变化时，会触发计算器执行。

#### 执行计算

`StructureFloorTopElevationCalculator`的`Execute`方法执行顶部标高的计算：

```cpp
void StructureFloorTopElevationCalculator::Execute()
{
    IInstance *pInstance = GetElement<IInstance>();
    double topElevation;
    if (StructureInstanceUtils::GetFloorTopElevation(pInstance, topElevation))
    {
        UniIdentity paramUId = PARAMETER_UID(TopElevationBuiltInParameter);
        StructureInstanceUtils::SetInstanceOutputParamValueByParamUid(pInstance, topElevation, paramUId);
    }
}
```

`StructureFloorBottomElevationCalculator`的`Execute`方法执行底部标高的计算：

```cpp
void StructureFloorBottomElevationCalculator::Execute()
{
    IInstance *pInstance = GetElement<IInstance>();
    double bottomElevation;
    if (StructureInstanceUtils::GetFloorBottomElevation(pInstance, bottomElevation))
    {
        UniIdentity paramUId = PARAMETER_UID(BottomElevationBuiltInParameter);
        StructureInstanceUtils::SetInstanceOutputParamValueByParamUid(pInstance, bottomElevation, paramUId);
    }
}
```

这些方法的执行流程是：

1. 获取楼板实例
2. 调用相应的工具方法计算楼板的顶部或底部标高
3. 将计算结果设置到相应的参数中

### 楼板坡度计算器

楼板的坡度计算通过`StructureFloorSlopeCalculator`系列类实现：

```cpp
class StructureFloorSlopeCalculatorBase :public GbmpCalculatorBase
{
public:
    StructureFloorSlopeCalculatorBase(IDocument* pDoc, const RegenDataId& outputDataId) : GbmpCalculatorBase(pDoc, outputDataId) {}
    virtual void ReportInputDataIds(std::vector<RegenDataId> & dataIds) const override;
};

class StructureFloorSlopeAngleCalculator : public StructureFloorSlopeCalculatorBase
{
    DECLARE_CALCULATOR(StructureFloorTopElevationCalculator, IInstance)
public:
    StructureFloorSlopeAngleCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : StructureFloorSlopeCalculatorBase(pDoc, outputDataId) {}
    virtual void Execute() override;
};

class StructureFloorSlopeStartHeightCalculator : public StructureFloorSlopeCalculatorBase
{
    DECLARE_CALCULATOR(StructureFloorSlopeStartHeightCalculator, IInstance)
public:
    StructureFloorSlopeStartHeightCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : StructureFloorSlopeCalculatorBase(pDoc, outputDataId) {}
    virtual void Execute() override;
};

class StructureFloorSlopeEndHeightCalculator : public StructureFloorSlopeCalculatorBase
{
    DECLARE_CALCULATOR(StructureFloorSlopeEndHeightCalculator, IInstance)
public:
    StructureFloorSlopeEndHeightCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : StructureFloorSlopeCalculatorBase(pDoc, outputDataId) {}
    virtual void Execute() override;
};
```

#### 报告输入数据依赖

`StructureFloorSlopeCalculatorBase`的`ReportInputDataIds`方法报告了计算器依赖的输入数据：

```cpp
void StructureFloorSlopeCalculatorBase::ReportInputDataIds(std::vector<RegenDataId> & dataIds) const
{
    const IInstance* pTarget = GetElement<IInstance>();
    const IElementModelShape* pGrepBehavior = pTarget->GetElementModelShape();

    // grep
    dataIds.push_back(pGrepBehavior->GetGraphicsElementShapeRdId());

    // position associated plane
    if (pTarget->GetElementPosition())
    {
        dataIds.push_back(pTarget->GetElementPosition()->GetAssociatedPlaneIdRdId());

        const IElement* pAssociatedLevel = GetDocument()->GetElement(pTarget->GetElementPosition()->GetBaseAssociatedPlaneId());
        if (pAssociatedLevel)
        {
            const IElementPositionReportComponent* pPositionReport = pAssociatedLevel->GetElementPositionReportComponent();
            if (!pPositionReport)
                return;
            pPositionReport->GetReferencePlaneRdId(dataIds);
        }
    }

    // slopeline id
    const StructureFloor* pFloorData = StructureFloor::Get(pTarget);
    if (pFloorData)
    {
        dataIds.push_back(pFloorData->GetSlopeLineIdRdId());
    }
}
```

这里报告了多个依赖数据：

1. 楼板的图形元素形状ID
2. 楼板的关联平面ID
3. 参考平面的ID
4. 坡度线的ID

当这些数据中的任何一个发生变化时，都会触发计算器执行。

#### 执行计算

`StructureFloorSlopeAngleCalculator`的`Execute`方法执行坡度角度的计算：

```cpp
void StructureFloorSlopeAngleCalculator::Execute()
{
    IInstance *pInstance = GetElement<IInstance>();
    double slopeAngle;
    if (StructureInstanceUtils::GetFloorSlopeAngle(pInstance, slopeAngle))
    {
        UniIdentity paramUId = PARAMETER_UID(FloorSlopeAngleBuiltInParameter);
        StructureInstanceUtils::SetInstanceOutputParamValueByParamUid(pInstance, slopeAngle, paramUId);
    }
}
```

`StructureFloorSlopeStartHeightCalculator`的`Execute`方法执行坡度起点高度的计算：

```cpp
void StructureFloorSlopeStartHeightCalculator::Execute()
{
    IInstance *pInstance = GetElement<IInstance>();
    double slopeStartHeight;
    double slopeEndHeight;
    if (StructureInstanceUtils::GetFloorSlopeStartEndHeight(pInstance, slopeStartHeight, slopeEndHeight))
    {
        UniIdentity paramUid = PARAMETER_UID(FloorSlopeStartHeightBuiltInParameter);
        StructureInstanceUtils::SetInstanceOutputParamValueByParamUid(pInstance, slopeStartHeight, paramUid);
    }
}
```

`StructureFloorSlopeEndHeightCalculator`的`Execute`方法执行坡度终点高度的计算：

```cpp
void StructureFloorSlopeEndHeightCalculator::Execute()
{
    IInstance *pInstance = GetElement<IInstance>();
    double slopeStartHeight;
    double slopeEndHeight;
    if (StructureInstanceUtils::GetFloorSlopeStartEndHeight(pInstance, slopeStartHeight, slopeEndHeight))
    {
        UniIdentity paramUid = PARAMETER_UID(FloorSlopeEndHeightBuiltInParameter);
        StructureInstanceUtils::SetInstanceOutputParamValueByParamUid(pInstance, slopeEndHeight, paramUid);
    }
}
```

这些方法的执行流程是：

1. 获取楼板实例
2. 调用相应的工具方法计算楼板的坡度相关参数
3. 将计算结果设置到相应的参数中

### 楼板面积计算器

楼板的面积计算通过`StructureFloorAreaCalculator`类实现：

```cpp
class StructureFloorAreaCalculator : public gcmp::GbmpCalculatorBase
{
    DECLARE_CALCULATOR(StructureFloorAreaCalculator, IInstance)
public:
    StructureFloorAreaCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : GbmpCalculatorBase(pDoc, outputDataId) {}
    virtual void ReportInputDataIds(std::vector<RegenDataId> & dataIds) const override;
    virtual void Execute() override;
};
```

#### 报告输入数据依赖

`StructureFloorAreaCalculator`的`ReportInputDataIds`方法报告了计算器依赖的输入数据：

```cpp
void StructureFloorAreaCalculator::ReportInputDataIds(std::vector<RegenDataId> & dataIds) const
{
    IInstance *pTarget = GetElement<IInstance>();
    const IElementModelShape *pGrepBehavior = pTarget->GetElementModelShape();
    dataIds.push_back(pGrepBehavior->GetGraphicsElementShapeRdId());
}
```

这里报告了楼板的图形元素形状ID，当楼板的形状发生变化时，会触发计算器执行。

#### 执行计算

`Execute`方法执行实际的计算：

```cpp
void StructureFloorAreaCalculator::Execute()
{
    IInstance *pInstance = GetElement<IInstance>();
    double area;
    if (StructureInstanceUtils::GetFloorTopArea(pInstance, area))
    {
        UniIdentity paramUId = PARAMETER_UID(AreaBuiltInParameter);
        StructureInstanceUtils::SetInstanceOutputParamValueByParamUid(pInstance, area, paramUId);
    }
}
```

这个方法的执行流程是：

1. 获取楼板实例
2. 调用StructureInstanceUtils::GetFloorTopArea计算楼板的面积
3. 将计算结果设置到AreaBuiltInParameter参数中

## 坡度线的父子关系管理机制

坡度线图元在GDMP中实现了复杂的父子关系管理机制，通过`ReportParents`和`UpdateForWeakParentDeletion`方法来管理与关联平面、参数绑定、图形引用等多种图元的依赖关系。

### 坡度线图元的父子关系实现

#### StructureSlopeLine实现

坡度线图元建立了多种类型的依赖关系：

```cpp
void gcmp::StructureSlopeLine::ReportParents(IElementParentReporter& reporter) const
{
    const IElement* pElement = GetOwnerElement();
    const IElementPosition *posBehavior = pElement->GetElementPosition();
    if (posBehavior != nullptr)
    {
        reporter.ReportStrong(posBehavior->GetBaseAssociatedPlaneId());
    }

    // 报告参数绑定的弱引用
    const IElementParameterBindings* pBehavior = GetOwnerElement()->GetElementParameters()->GetElementParameterBindings();
    if (pBehavior)
    {
        auto& bindingInfos = pBehavior->GetParameterBindings();
        FOR_EACH(info, bindingInfos)
        {
            IElement* pSourceElement = m_pOwnerElement ? m_pOwnerElement->GetDocument()->GetElement(info->GetSourceElementId()) : nullptr;
            if (pSourceElement != nullptr)
            {
                reporter.ReportWeak(pSourceElement->GetElementId());
            }
        }
    }

    // 报告嵌入参数的弱引用
    auto& embeddedParamMap = GetOwnerElement()->GetElementParameters()->GetEmbeddedParametersMap();
    FOR_EACH(iter, embeddedParamMap)
    {
        const IParameterValueElementId *pElementIdParamValue = dynamic_cast<const IParameterValueElementId *>(iter.second->GetParameterValueStorage());
        if (pElementIdParamValue != nullptr)
        {
            reporter.ReportWeak(pElementIdParamValue->GetValue());
        }
    }

    reporter.ReportStrong(GetOwnerElement()->GetBasicInformation()->GetTypeId());

    // 报告图形引用的弱引用
    const IGraphicsElementShape* pGrep = pElement->GetElementModelShape()->GetGraphicsElementShape();
    if (pGrep)
    {
        std::vector<ElementId> ids;
        pGrep->GetReferencedElementIds(ids);
        reporter.ReportWeak(ids);
    }

    reporter.ReportStrong(this->GetInstanceId());
}
```

```cpp
void gcmp::StructureSlopeLine::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
{
    IElement* pElement = GetOwnerElement();

    // 处理参数绑定的弱引用删除
    // 具体实现包括清理无效的参数绑定关系
}
```

### 坡度线的多重依赖关系

#### 强引用关系

1. **关联平面依赖**：坡度线对其关联平面采用强引用，确保平面删除时坡度线也被删除
2. **类型依赖**：坡度线对其类型图元采用强引用，保证类型定义的完整性
3. **实例依赖**：坡度线对其实例采用强引用，确保实例的完整性

#### 弱引用关系

1. **参数绑定依赖**：坡度线对参数绑定的源图元采用弱引用，源图元删除时清理绑定关系
2. **嵌入参数依赖**：坡度线对嵌入参数中引用的图元采用弱引用
3. **图形引用依赖**：坡度线对图形表达中引用的图元采用弱引用，支持图形引用的动态管理

### 设计特点

坡度线的父子关系管理具有以下特点：

1. **多重依赖**：坡度线建立了强引用和弱引用的多重依赖关系，类似于轴网的实现
2. **参数绑定处理**：对参数绑定采用弱引用，支持动态绑定管理
3. **图形引用管理**：对图形表达中的引用采用弱引用，支持图形的灵活组织
4. **类型安全**：对关联平面、类型和实例采用强引用，确保几何和类型的一致性
