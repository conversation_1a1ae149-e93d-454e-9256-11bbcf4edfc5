# 拾取Action的GDMP框架集成分析

## 概述

本文档分析GDMPLab中拾取Action与GDMP框架的集成方式，包括核心接口的使用、框架服务的调用、以及与框架组件的交互机制。拾取Action作为GDMP框架的重要组成部分，深度集成了框架提供的各种服务和接口。

## 核心框架接口集成

### 1. Action管理器集成 (IActionManager)

#### 接口定义
```cpp
class IActionManager
{
public:
    // 执行Action操作
    static bool ExecuteAction(OwnerPtr<IAction> opAction);
    
    // 获取当前Action
    static IAction* CurrentAction();
    
    // 重置所有Action
    static void Reset();
    
    // 获取提示信息
    static std::vector<std::wstring> GetPromptMessages();
};
```

#### 集成方式
```cpp
// 启动拾取Action
void StartPickAction()
{
    // 创建拾取Action实例
    PickNodeReferenceExchangeData exchangeData;
    OwnerPtr<SamplePickNodeReferenceAction> opPickAction = 
        NEW_AS_OWNER_PTR(SamplePickNodeReferenceAction, 
                        exchangeData, 
                        moveCallback, 
                        bMultiSelect, 
                        finishedMode);
    
    // 通过Action管理器执行
    IActionManager::ExecuteAction(TransferOwnership(opPickAction));
}

// 在Action内部获取当前Action状态
void SamplePickActionBase::SomeMethod()
{
    IAction* pCurrentAction = IActionManager::CurrentAction();
    if (pCurrentAction == this)
    {
        // 当前Action正在执行
    }
}
```

#### 生命周期管理
- **启动**: 通过`IActionManager::ExecuteAction()`启动Action
- **执行**: Action在框架的事件循环中接收和处理事件
- **结束**: 通过`MarkFinishStatus()`标记完成状态，框架自动清理

### 2. 选择集管理集成 (ISelection)

#### 接口定义
```cpp
class ISelection : public IObject
{
public:
    static ISelection* Get();  // 获取单例实例
    
    // 基础操作
    virtual void Clear(IDocument* pDocument) = 0;
    virtual bool Add(IDocument* pDocument, const ElementId& elementId) = 0;
    virtual bool Remove(IDocument* pDocument, const ElementId& elementId) = 0;
    
    // 批量操作
    virtual bool AddGroupGraphicsNodeReference(IDocument* pDocument, 
        const GraphicsNodeReferenceOwnerPtrSet& graphicsNodeReferences) = 0;
    virtual bool ReplaceGroupGraphicsNodeReference(IDocument* pDocument, 
        const GraphicsNodeReferenceOwnerPtrSet& graphicsNodeReferences) = 0;
    virtual bool RevertGroupGraphicsNodeReference(IDocument* pDocument, 
        const GraphicsNodeReferenceOwnerPtrSet& graphicsNodeReferences) = 0;
    
    // 查询操作
    virtual int GetCount() const = 0;
    virtual const GraphicsNodeReferenceOwnerPtrSet& GetGraphicsNodeReferences() = 0;
};
```

#### 集成实现
```cpp
void SamplePickActionBase::AddToSelection(IDocument* pDocument, 
                                         const IGraphicsNodeReference& pickResult)
{
    // 获取全局选择集单例
    ISelection* pGlobalSelection = ISelection::Get();
    
    // 根据键盘状态执行不同操作
    bool ctrlKeyPressed = IsKeyAndButtonPressed(VK_CONTROL);
    bool shiftKeyPressed = IsKeyAndButtonPressed(VK_SHIFT);
    
    if (ctrlKeyPressed && !shiftKeyPressed)
    {
        // Ctrl键：添加到选择集
        pGlobalSelection->AddGraphicsNodeReference(pDocument, pickResult);
    }
    else if (!ctrlKeyPressed && shiftKeyPressed)
    {
        // Shift键：从选择集移除
        pGlobalSelection->DeleteGraphicsNodeReference(pDocument, pickResult);
    }
    else if (!ctrlKeyPressed && !shiftKeyPressed)
    {
        // 无修饰键：替换选择集
        GraphicsNodeReferenceOwnerPtrSet nodeRefs;
        nodeRefs.insert(TransferOwnership(pickResult.Clone()));
        pGlobalSelection->ReplaceGroupGraphicsNodeReference(pDocument, nodeRefs);
    }
    else if (ctrlKeyPressed && shiftKeyPressed)
    {
        // Ctrl+Shift：反转选择状态
        GraphicsNodeReferenceOwnerPtrSet nodeRefs;
        nodeRefs.insert(TransferOwnership(pickResult.Clone()));
        pGlobalSelection->RevertGroupGraphicsNodeReference(pDocument, nodeRefs);
    }
}
```

### 3. 高亮管理集成 (IHighlights)

#### 接口定义
```cpp
class IHighlights : public IObject
{
public:
    static IHighlights* Get();  // 获取单例实例
    
    // 高亮操作
    virtual void Clear() = 0;
    virtual void AddGraphicsNodeReferences(
        const GraphicsNodeReferenceOwnerPtrVector& graphicsNodeReferences) = 0;
    virtual void RemoveGraphicsNodeReferences(
        const GraphicsNodeReferenceOwnerPtrVector& graphicsNodeReferences) = 0;
    
    // 查询操作
    virtual const GraphicsNodeReferenceOwnerPtrVector& GetAllGraphicsNodeReferences() = 0;
    virtual int GetCount() const = 0;
};
```

#### 集成实现
```cpp
void SamplePickActionBase::UpdateHighlightOnlySelection()
{
    // 获取当前选择集
    const auto& selectionGNodeRefs = ISelection::Get()->GetGraphicsNodeReferences();
    std::vector<OwnerPtr<IGraphicsNodeReference>> highLightGNodeRefs;
    
    // 转换为高亮集合格式
    OwnerPtrContainerUtil::AddItems(highLightGNodeRefs, selectionGNodeRefs);
    
    // 更新高亮显示
    IHighlights* pHighlights = IHighlights::Get();
    pHighlights->Clear();
    pHighlights->AddGraphicsNodeReferences(highLightGNodeRefs);
    
    // 单选时添加关联对象高亮
    if (ISelection::Get()->GetCount() == 1)
    {
        ElementId elementId = selectionGNodeRefs.begin()->get()->GetElementId();
        auto associatedObjects = GetElementAssociatedSelectionObjects(elementId);
        pHighlights->AddGraphicsNodeReferences(associatedObjects);
    }
}
```

### 4. UI管理器集成 (IUiManager)

#### 接口使用
```cpp
void ActionUiBehavior::InitializeUi()
{
    if (m_bMultiSelectMode && !m_bInitialized)
    {
        // 获取UI管理器
        IUiManager* pUiMgr = IUiManager::Get();
        
        // 获取主窗口
        IApplicationWindow* pUiMainWnd = pUiMgr->GetApplicationWindow();
        
        // 显示应用/取消按钮
        pUiMainWnd->ShowApplyOrCancelGui(
            GBMP_TR(L"按范围拾取"), 
            ID_CMD_APPLY_PickNodeReferenceAction, 
            ID_CMD_CANCEL_PickNodeReferenceAction
        );
        
        m_bInitialized = true;
    }
}

void ActionUiBehavior::UnInitializeUi()
{
    if (m_bMultiSelectMode && m_bInitialized)
    {
        // 隐藏UI元素
        IUiManager* pUiMgr = IUiManager::Get();
        IApplicationWindow* pUiMainWnd = pUiMgr->GetApplicationWindow();
        pUiMainWnd->HideApplyOrCancelGui();
        
        // 清理高亮和刷新视图
        IHighlights::Get()->Clear();
        
        // 刷新画布
        IUiDocumentViewManager* pViewMgr = IUiDocumentViewManager::Get();
        if (pViewMgr && pViewMgr->GetCurrentUiView())
        {
            ICanvas* pCanvas = pViewMgr->GetCurrentUiView()->GetCanvas();
            pCanvas->Refresh();
        }
        
        m_bInitialized = false;
    }
}
```

## 框架服务集成

### 1. 拾取服务集成

#### 拾取工具类集成
```cpp
class GbmpPickActionUtil
{
public:
    // 核心拾取方法 - 集成框架拾取服务
    static void UpdateCandidatesSingleton(
        IUiView* pCurrentView,
        int screenX, int screenY,
        const Vector3d& pos,
        const IPickEvent* pPickPostProcesserEvent = nullptr,
        const IPickFilter* pPickFilter = nullptr,
        const IPickTarget* pPickTarget = nullptr,
        bool isPickingHighlightOnlyGraphicsNodeAllowed = false,
        bool selectByFaceInteriorEnabled = true,
        int pickTolerance = -1,
        bool isHoveringHighlight = true);
};

// 在Action中使用框架拾取服务
bool SamplePickActionBase::UpdateCandidates(IUiView* pCurrentView, const Vector3d& pos)
{
    // 调用框架拾取服务
    GbmpPickActionUtil::UpdateCandidatesSingleton(
        pCurrentView,
        screenX, screenY,
        pos,
        GetPickPostProcessEvent(),
        m_opPickFilter.get(),
        m_opPickTarget.get(),
        false,
        true,
        m_pickPixelTolerance,
        true
    );
    
    return true;
}
```

### 2. 事件系统集成

#### 拾取事件处理
```cpp
// 事件处理器接口
class IPickEventHandler
{
public:
    virtual void HandlePickEvent(IUiView* pView, const Vector3d& pos) = 0;
};

// 事件管理接口
class IPickEvent
{
public:
    virtual void Add(IPickEventHandler* handler) = 0;
    virtual void Remove(IPickEventHandler* handler) = 0;
    virtual void Execute(IUiView* pView, const Vector3d& pos) = 0;
};

// 在Action中集成事件系统
class SamplePickNodeReferenceAction
{
public:
    void AddPostProcesserEventHandler(IPickEventHandler* pPostEventHandler)
    {
        // 添加到框架事件系统
        GetPickPostProcessEvent()->Add(pPostEventHandler);
    }
};
```

### 3. 文档系统集成

#### 文档访问和操作
```cpp
class SamplePickActionBase : public ActionBase
{
protected:
    // 获取当前文档
    IDocument* GetDoc() const
    {
        return ActionBase::GetDoc();  // 继承自框架基类
    }
    
    // 文档操作示例
    void ProcessElement(const ElementId& elementId)
    {
        IDocument* pDoc = GetDoc();
        if (pDoc)
        {
            // 通过框架文档接口访问元素
            const IElement* pElement = pDoc->GetElement(elementId);
            if (pElement)
            {
                // 处理元素
            }
        }
    }
};
```

## 框架扩展点集成

### 1. 接口实现扩展
```cpp
// 实现框架定义的接口
class PickFilter : public gcmp::IPickFilter
{
public:
    // 实现框架接口方法
    virtual bool AllowElement(const ElementId& elemId) const override;
    virtual bool AllowGraphicsNode(const IGraphicsNodeReference& nodeRef) const override;
    virtual bool SetPickTargetOption(IPickTarget* pickTarget) override;
};

class ActionUiBehavior : public gcmp::IActionUiBehavior
{
public:
    // 实现框架接口方法
    virtual void InitializeUi() override;
    virtual void UnInitializeUi() override;
    virtual OwnerPtr<IActionUiBehavior> Clone() const override;
};
```

### 2. 回调机制集成
```cpp
// 框架回调函数类型定义
using MoveCallback = std::function<void(IUiView*, const Vector3d&)>;
using OnKeyDownCallback = std::function<bool(int, IUiView*, const Vector3d&)>;

class SamplePickNodeReferenceAction
{
private:
    MoveCallback m_moveCallback;
    OnKeyDownCallback m_OnKeyDownCallback;
    
public:
    // 在框架事件中调用回调
    virtual bool OnMovePoint(IUiView* pCurrentView, const Vector3d& pos) override
    {
        // 调用用户定义的回调
        if (m_moveCallback)
        {
            m_moveCallback(pCurrentView, pos);
        }
        
        return SamplePickActionBase::OnMovePoint(pCurrentView, pos);
    }
};
```

## 框架集成优势

### 1. 统一的架构模式
- **一致性**: 遵循框架定义的接口和模式
- **互操作性**: 与其他框架组件无缝集成
- **标准化**: 使用框架标准的数据结构和方法

### 2. 丰富的框架服务
- **拾取服务**: 利用框架高效的拾取算法
- **选择集管理**: 使用框架统一的选择集服务
- **高亮显示**: 集成框架的渲染和高亮系统
- **UI管理**: 利用框架的UI组件和布局系统

### 3. 扩展性和可维护性
- **接口抽象**: 通过接口实现功能扩展
- **事件驱动**: 基于框架事件系统的松耦合设计
- **回调机制**: 支持用户自定义行为注入
- **生命周期管理**: 框架自动管理对象生命周期

## 集成最佳实践

### 1. 接口使用规范
- 始终通过框架接口访问服务
- 正确处理接口返回的智能指针
- 遵循框架的错误处理模式

### 2. 资源管理
- 使用框架提供的智能指针类型
- 正确实现对象的创建和销毁
- 避免内存泄漏和悬空指针

### 3. 事件处理
- 正确实现事件处理接口
- 及时注册和注销事件处理器
- 避免事件处理中的阻塞操作

## 总结

拾取Action与GDMP框架的深度集成体现了良好的架构设计。通过标准接口、统一服务、扩展机制和回调系统，实现了功能的模块化和可扩展性。这种集成方式不仅提供了强大的功能支持，还保证了代码的一致性和可维护性，为构建复杂的三维交互应用提供了坚实的基础。
