# ActionModify 选择集管理

## 概述

选择集管理是 ActionModify 的核心功能之一，负责维护当前选中的图元集合，处理选择集的增加、删除、清空等操作，并管理选择集变化时的通知机制。GDMPLab 支持两种选择模式：传统模式和 CAD 风格模式，两种模式在交互行为上有显著差异。

## 选择模式架构

### 选择模式控制

```cpp
class GbmpPickActionUtil
{
public:
    static bool IS_CAD_STYLE_SELECT;  // CAD 风格选择标志
    // false: 传统模式 (默认)
    // true:  CAD 风格模式
};
```

### 两种模式的核心差异

| 特性 | 传统模式 | CAD 风格模式 |
|------|----------|--------------|
| 点击空白区域 | 清空选择集 | 保持选择集 |
| 单击已选对象 | 替换选择集 | 累加到选择集 |
| 框选触发 | 按下拖拽 | 点击空白后拖拽 |
| 修饰键依赖 | 强依赖 Ctrl/Shift | 弱依赖修饰键 |

## 核心接口设计

### ISelection 全局选择集管理器

GDMP 提供的全局选择集管理器：

```cpp
class ISelection
{
public:
    static ISelection* Get();  // 获取单例实例

    // 选择集操作
    void Clear(IDocument* pDoc);                    // 清空选择集
    void Add(IDocument* pDoc, const ElementId& id); // 添加图元到选择集
    void Remove(IDocument* pDoc, const ElementId& id); // 从选择集移除图元

    // 批量操作
    void AddGroupGraphicsNodeReference(IDocument* pDoc, const GraphicsNodeReferenceOwnerPtrSet& refs);
    void ReplaceGroupGraphicsNodeReference(IDocument* pDoc, const GraphicsNodeReferenceOwnerPtrSet& refs);
    void RevertGroupGraphicsNodeReference(IDocument* pDoc, const GraphicsNodeReferenceOwnerPtrSet& refs);

    // 选择集查询
    int GetCount() const;                           // 获取选择集数量
    bool Contains(const ElementId& id) const;       // 检查是否包含指定图元
    std::vector<ElementId> GetAllElementIds() const; // 获取所有选中的图元ID
};
```

### IHighlights 高亮管理器

管理选中图元的高亮显示：

```cpp
class IHighlights
{
public:
    static IHighlights* Get();  // 获取单例实例

    void Clear();  // 清空高亮
    void AddGraphicsNodeReferences(const GraphicsNodeReferenceOwnerPtrVector& refs);
    void SetGraphicsNodeReferences(const GraphicsNodeReferenceOwnerPtrVector& refs);
};
```

## 选择操作实现

### 单对象选择

#### 基础选择方法

```cpp
void GbmpPickActionBase::AddToSelection(IDocument* pDocument,
                                       const IGraphicsNodeReference& pickResult,
                                       const IUiView* pCurrentView)
{
    // 检查是否可以被选择
    if (!IsCanBeSelect(pDocument, pickResult))
        return;

    // 检查编辑模式权限
    IEditMode* pEditModeUE = IEditMode::GetTopActiveEditMode(pDocument);
    if (pEditModeUE)
    {
        const IElement* pElement = pDocument->GetElement(pickResult.GetElementId());
        if (!pEditModeUE->IsElementEditable(pElement))
            return;
    }

    // 添加到选择集
    ISelection::Get()->Add(pDocument, pickResult.GetElementId());

    // 更新高亮
    GraphicsNodeReferenceOwnerPtrVector highlights;
    highlights.push_back(pickResult.Clone());
    IHighlights::Get()->AddGraphicsNodeReferences(highlights);
}
```

#### 模式差异处理

```cpp
// 单对象选择的模式差异处理
GraphicsNodeReferenceOwnerPtrSet nodeRefs;
nodeRefs.insert(TransferOwnership(pickResult.Clone()));

// 传统模式：不按修饰键时替换选择集
// CAD模式：不按修饰键时累加到选择集
if (!ctrlKeyPressed && !shiftKeyPressed && !IsDraggingAwaySelectedElementsEnabled(pCurrentView))
{
    if (GbmpPickActionUtil::IS_CAD_STYLE_SELECT)
    {
        pGlobalSelection->AddGroupGraphicsNodeReference(pDoc, nodeRefs);      // CAD模式：累加
    }
    else
    {
        pGlobalSelection->ReplaceGroupGraphicsNodeReference(pDoc, nodeRefs);  // 传统模式：替换
    }
}
```

### 批量选择（框选）

#### 框选结果处理

```cpp
void GbmpPickActionBase::AddToSelectionGroup(IDocument* pDoc,
                                            GraphicsNodeReferenceOwnerPtrSet& pickResults)
{
    if (pickResults.empty())
        return;

    // 检查是否有表格在选择集中
    bool hasTable = false;
    HasTableInSelection(pDoc, pickResults, hasTable);

    // 模式差异处理
    bool ctrlKeyPressed = IsKeyAndButtonPressed(VK_CONTROL);
    bool shiftKeyPressed = IsKeyAndButtonPressed(VK_SHIFT);

    // 传统模式：不按修饰键时替换选择集
    // CAD模式：不按修饰键时累加到选择集
    if (!ctrlKeyPressed && !shiftKeyPressed)
    {
        if (GbmpPickActionUtil::IS_CAD_STYLE_SELECT)
        {
            pGlobalSelection->AddGroupGraphicsNodeReference(pDoc, pickResults);      // CAD模式：累加
        }
        else
        {
            pGlobalSelection->ReplaceGroupGraphicsNodeReference(pDoc, pickResults);  // 传统模式：替换
        }
    }

    // Ctrl+Shift：求反操作（两种模式相同）
    if (ctrlKeyPressed && shiftKeyPressed && !hasTable)
    {
        pGlobalSelection->RevertGroupGraphicsNodeReference(pDoc, pickResults);
    }
}
```

### 选择集清理

```cpp
void GbmpPickActionBase::ClearSelection(IDocument* pDoc)
{
    ISelection::Get()->Clear(pDoc);
    IHighlights::Get()->Clear();
}

bool GbmpPickActionBase::IsSelectionEmpty()
{
    return ISelection::Get()->GetCount() <= 0;
}
```

## CAD 风格选择详细分析

### 核心机制

CAD 风格选择通过 `GbmpPickActionUtil::IS_CAD_STYLE_SELECT` 标志控制，改变了传统的选择交互模式。

#### 状态标记系统

```cpp
class GbmpPickActionBase
{
private:
    unsigned int m_status;                    // 状态标记
    bool m_isCADRectSelectionFlag;           // CAD矩形选择标记
    Vector3d m_worldStart;                   // 起始点
};

// 状态标记定义
#define PS_GBMP_LBUTTON_DOWN    0x01    // 左键按下
#define PS_GBMP_LBUTTON_UP      0x02    // 左键抬起
#define PS_GBMP_LBUTTON_MOVING  0x04    // 左键移动
```

### 点击行为差异

#### 传统模式点击逻辑

```cpp
bool GbmpPickActionBase::OnLButtonDown(IUiView* pCurrentView, const Vector3d& pos)
{
    if (!GbmpPickActionUtil::IS_CAD_STYLE_SELECT)
    {
        // 传统模式：立即设置起始点，准备框选
        m_worldStart = pos;
        m_status = PS_GBMP_LBUTTON_DOWN;

        // 没有修饰键时清空选择集
        bool clear = !IsKeyAndButtonPressed(VK_CONTROL) &&
                    !IsKeyAndButtonPressed(VK_SHIFT) &&
                    !IsDraggingAwaySelectedElementsEnabled(pCurrentView);

        if (IPickCandidates::Get()->GetCount() == 0 && clear)
        {
            ClearSelection(pDoc);
            OnSelectionChanged();
            return false;
        }
    }
}
```

#### CAD 模式点击逻辑

```cpp
bool GbmpPickActionBase::OnLButtonDown(IUiView* pCurrentView, const Vector3d& pos)
{
    if (GbmpPickActionUtil::IS_CAD_STYLE_SELECT)
    {
        // CAD模式：根据是否有候选项决定行为
        if (!m_isCADRectSelectionFlag)
        {
            m_worldStart = pos;
            if (IPickCandidates::Get()->IsEmpty())
            {
                // 点击空白：准备框选，不清空选择集
                m_isCADRectSelectionFlag = true;
            }
            // 点击对象：直接选择，累加到选择集
        }
        else
        {
            // 结束框选状态
            m_isCADRectSelectionFlag = false;
        }
    }
}
```

### 框选触发机制

#### 传统模式框选

```cpp
// 传统模式：按下即开始框选
bool isRectSelect = (m_status & PS_GBMP_LBUTTON_DOWN) && !GbmpPickActionUtil::IS_CAD_STYLE_SELECT;
```

#### CAD 模式框选

```cpp
// CAD模式：点击空白后才能框选
bool isCADStyleRectSelect = !(m_status & PS_GBMP_LBUTTON_DOWN) &&
                           GbmpPickActionUtil::IS_CAD_STYLE_SELECT &&
                           m_isCADRectSelectionFlag;
```

### 移动事件处理差异

```cpp
bool GbmpPickActionBase::OnMovePoint(IUiView* pCurrentView, const Vector3d& pos)
{
    bool isCADStyleRectSelect = !(m_status & PS_GBMP_LBUTTON_DOWN) &&
                               GbmpPickActionUtil::IS_CAD_STYLE_SELECT;

    // 判断是否进入框选模式
    if (((m_status & PS_GBMP_LBUTTON_DOWN) && !GbmpPickActionUtil::IS_CAD_STYLE_SELECT) ||
        isCADStyleRectSelect)
    {
        // 传统模式：已选中对象且无修饰键时不允许框选
        // CAD模式：允许在有选择集的情况下继续框选
        if ((!IsSelectionEmpty() &&
            !IsKeyAndButtonPressed(VK_CONTROL) &&
            !GbmpPickActionUtil::IS_CAD_STYLE_SELECT &&  // 传统模式限制
            !IsKeyAndButtonPressed(VK_SHIFT)) ||
            (IsDraggingAwaySelectedElementsEnabled(pCurrentView)))
        {
            return true;  // 传统模式：阻止框选
        }

        if (isCADStyleRectSelect)
        {
            if (!m_isCADRectSelectionFlag)
            {
                // CAD模式：更新候选项而不是框选
                if (IsPickVisible())
                    isUpdatedView = UpdateCandidates(pCurrentView, pos);
                return true;
            }
        }

        // 绘制框选矩形
        DrawSelectRectangle(pCurrentView, pos, isReversePick);
    }
}
```

### 抬起事件处理

```cpp
bool GbmpPickActionBase::OnLButtonUp(IUiView* pCurrentView, const Vector3d& pos)
{
    // CAD模式特殊处理
    if (GbmpPickActionUtil::IS_CAD_STYLE_SELECT && (m_status & PS_GBMP_LBUTTON_MOVING))
    {
        m_status = PS_GBMP_LBUTTON_UP;
        m_isCADRectSelectionFlag = false;  // 重置框选标记
        pCanvas->Refresh();
        return true;
    }

    // 传统模式：有选择集且无修饰键时不处理框选
    if (!IsSelectionEmpty() &&
        !IsKeyAndButtonPressed(VK_CONTROL) &&
        !IsKeyAndButtonPressed(VK_SHIFT) &&
        !GbmpPickActionUtil::IS_CAD_STYLE_SELECT)  // 传统模式限制
    {
        return true;
    }
}
```

### 选择集变化通知

```cpp
class GbmpPickActionBase
{
protected:
    // 选择集变化时的回调方法，子类可以重写
    virtual void OnSelectionChanged();
};

class GbmpActionModify : public GbmpPickActionBase
{
protected:
    virtual void OnSelectionChanged() override;
};
```

## 修饰键与多选逻辑

### 修饰键行为矩阵

| 修饰键组合 | 传统模式 | CAD 模式 | 说明 |
|------------|----------|----------|------|
| 无修饰键 | 替换选择集 | 累加到选择集 | 核心差异 |
| Ctrl | 累加到选择集 | 累加到选择集 | 相同行为 |
| Shift | 范围选择 | 范围选择 | 相同行为 |
| Ctrl+Shift | 求反选择 | 求反选择 | 相同行为 |

### 修饰键检测与处理

```cpp
bool GbmpPickActionBase::OnLButtonDown(IUiView* pCurrentView, const Vector3d& pos)
{
    // 检测修饰键状态
    bool ctrlKeyPressed = IsKeyAndButtonPressed(VK_CONTROL);
    bool shiftKeyPressed = IsKeyAndButtonPressed(VK_SHIFT);

    // 传统模式：无修饰键时清空选择集
    if (!GbmpPickActionUtil::IS_CAD_STYLE_SELECT)
    {
        bool clear = !ctrlKeyPressed && !shiftKeyPressed &&
                    !IsDraggingAwaySelectedElementsEnabled(pCurrentView);

        if (IPickCandidates::Get()->GetCount() == 0 && clear)
        {
            ClearSelection(pDoc);
            OnSelectionChanged();
            return false;
        }
    }
    // CAD模式：保持选择集，通过后续逻辑处理累加
}
```

### 选择集操作策略

```cpp
// 1. Ctrl键：总是累加（两种模式相同）
if (ctrlKeyPressed && !shiftKeyPressed)
{
    pGlobalSelection->AddGroupGraphicsNodeReference(pDoc, pickResults);
}

// 2. Shift键：范围选择（两种模式相同）
if (!ctrlKeyPressed && shiftKeyPressed)
{
    // 范围选择逻辑
}

// 3. 无修饰键：模式差异的核心
if (!ctrlKeyPressed && !shiftKeyPressed)
{
    if (GbmpPickActionUtil::IS_CAD_STYLE_SELECT)
    {
        pGlobalSelection->AddGroupGraphicsNodeReference(pDoc, pickResults);      // CAD：累加
    }
    else
    {
        pGlobalSelection->ReplaceGroupGraphicsNodeReference(pDoc, pickResults);  // 传统：替换
    }
}

// 4. Ctrl+Shift：求反操作（两种模式相同）
if (ctrlKeyPressed && shiftKeyPressed)
{
    pGlobalSelection->RevertGroupGraphicsNodeReference(pDoc, pickResults);
}
```

## 选择过滤与权限控制

### 辅助对象过滤

```cpp
bool GbmpPickActionBase::IsAuxiliaryElement(const IDocument* pDoc, const ElementId& id)
{
    const IElement* pElement = pDoc->GetElement(id);
    if (!pElement)
        return false;

    // 检查是否为图元形状句柄（辅助对象）
    IElementShapeHandle* pShapeHandle = quick_cast<IElementShapeHandle>(pElement);
    return (pShapeHandle != nullptr);
}

// 框选时过滤辅助对象
class RemoveAuxiliaryElementHelper
{
public:
    RemoveAuxiliaryElementHelper(const IDocument* pDoc) : m_pDoc(pDoc) {}
    bool operator()(const OwnerPtr<IGraphicsNodeReference>& ref) const
    {
        IElement* pElement = m_pDoc->GetElement(ref->GetElementId());
        IElementShapeHandle* pShapeHandle = quick_cast<IElementShapeHandle>(pElement);
        return (pShapeHandle != nullptr);
    }
private:
    const IDocument* m_pDoc;
};
```

### 编辑权限检查

```cpp
bool GbmpPickActionBase::IsCanBeSelect(IDocument* pDoc, const IGraphicsNodeReference& refAdd)
{
    // 检查编辑模式权限
    IEditMode* pEditModeUE = IEditMode::GetTopActiveEditMode(pDoc);
    if (pEditModeUE)
    {
        const IElement* pElement = pDoc->GetElement(refAdd.GetElementId());
        if (!pEditModeUE->IsElementEditable(pElement))
            return false;
    }
    return true;
}
```

### 框选结果处理流程

```cpp
bool GbmpPickActionBase::OnLButtonUp(IUiView* pCurrentView, const Vector3d& pos)
{
    // 获取框选结果
    OwnerPtr<IPickResult> pickResults = GetPickResults(pCurrentView, pos, isReversePick);

    if (!pickResults->IsEmpty())
    {
        std::vector<OwnerPtr<IPick>>& picks = pickResults->GetAllPicks();
        GraphicsNodeReferenceOwnerPtrVector& pickData = picks.at(0)->GetAllGraphicsNodeReferencesFw();

        // 1. 过滤辅助对象
        RemoveAuxiliaryElementHelper removeHelper(pDoc);
        OwnerPtrContainerUtil::RemoveItemsIf(pickData, removeHelper);

        // 2. 检查编辑权限并构建有效选择集
        GraphicsNodeReferenceOwnerPtrSet pickSets;
        IEditMode* pEditModeUE = IEditMode::GetTopActiveEditMode(pDoc);

        for (auto iter = pickData.begin(); iter != pickData.end(); ++iter)
        {
            if (IsAuxiliaryElement(pDoc, (*iter)->GetElementId()))
                continue;

            const IElement* pElement = pDoc->GetElement((*iter)->GetElementId());
            if (pEditModeUE && !pEditModeUE->IsElementEditable(pElement))
                continue;

            OwnerPtrContainerUtil::AddItem(pickSets, **iter);
        }

        // 3. 添加到选择集
        AddToSelectionGroup(pDoc, pickSets);
    }

    OnSelectionChanged();
}
```

## 特殊对象处理

### 表格选择处理

```cpp
void GbmpPickActionBase::HasTableInSelection(IDocument* pDoc,
                                           const GraphicsNodeReferenceOwnerPtrSet& selections,
                                           bool& hasTable) const
{
    hasTable = false;
    for (const auto& selection : selections)
    {
        const IElement* pElement = pDoc->GetElement(selection->GetElementId());
        if (pElement && IsTableElement(pElement))
        {
            hasTable = true;
            break;
        }
    }
}

// 表格选择时的特殊夹点处理
if (DEBUG_MODE(CtlrDontNeedTableItemGripPoints))
{
    // Ctrl 选择不需要生成单元格夹点
    IDrawingTableGripPointsConfig::Get()->SetIsNeedDrawingTableItemGripPoints(false);
}
```

## 性能优化与调试支持

### 预高亮性能优化

选择集管理中最重要的性能优化是预高亮的处理，避免不必要的视图刷新：

```cpp
bool GbmpPickActionBase::UpdateCandidates(IUiView* pCurrentView, const Vector3d& pos)
{
    // 保存旧的预高亮状态
    const GraphicsNodeReferenceOwnerPtrVector& preHighlights =
        IPreHighlights::Get()->GetAllGraphicsNodeReferences();
    GraphicsNodeReferenceOwnerPtrVector oldPreHighlights;
    OwnerPtrContainerUtil::AddItems(oldPreHighlights, preHighlights);

    // 更新候选项
    GbmpPickActionUtil::UpdateCandidatesSingleton(pCurrentView, screenX, screenY,
                                                  m_pickPixelTolerance, m_upPickFilter.get());

    // 获取新的预高亮状态
    const GraphicsNodeReferenceOwnerPtrVector& newPreHighlights =
        IPreHighlights::Get()->GetAllGraphicsNodeReferences();

    // 关键优化：只有当预高亮变化时，才刷新视图
    if (!OwnerPtrContainerUtil::IsSameContentContainer(oldPreHighlights, newPreHighlights))
    {
        UpdateView();
        return true;
    }

    return false;
}
```

### 批量操作优化

```cpp
// 集中设置选择集和高亮，减少性能问题
GraphicsNodeReferenceOwnerPtrSet newSelections;
std::vector<OwnerPtr<IGraphicsNodeReference>> newHighlights;

FOR_EACH(elementId, allElementIds)
{
    OwnerPtr<IGraphicsNodeReference> opGnodeRef =
        IGraphicsNodeReference::CreateGraphicsElementShapeReference(elementId);
    newSelections.insert(opGnodeRef->Clone());
    newHighlights.push_back(TransferOwnership(opGnodeRef));
}

// 批量设置，避免多次触发更新事件
ISelection::Get()->AddGroupGraphicsNodeReference(pDoc, newSelections);
IHighlights::Get()->AddGraphicsNodeReferences(newHighlights);
```

### 大场景优化配置

```cpp
// 大场景下的选择集处理优化
if (DEBUG_MODE(GrepSizeThresholdForLargeScene))
{
    pickContext->SetGrepSizeThresholdForLargeScene(1000);
}
```

### 调试模式支持

```cpp
// 表格选择相关调试模式
CREATE_DEBUG_MODE(CtlrDontNeedTableItemGripPoints,
    L"Ctrl选择不需要生成单元格夹点",
    gcmp::DebugModeGroup::DMGT_GREP_AND_PICKSNAP,
    L"GDMPLab", L"2023-12-20");

CREATE_DEBUG_MODE(ShiftUpdateSelectionSelectionForTable,
    L"Shift按下处理表格选择集更新",
    gcmp::DebugModeGroup::DMGT_GREP_AND_PICKSNAP,
    L"GDMPLab", L"2023-12-20");

// 状态栏信息控制
CREATE_DEBUG_MODE(GBMPDontPrintCandidateOnStatusBar,
    L"不在状态栏显示候选项信息",
    gcmp::DebugModeGroup::DMGT_GREP_AND_PICKSNAP,
    L"GDMPLab", L"2023-12-20");
```

### 状态栏信息显示

```cpp
std::wstring GbmpPickActionBase::GetPromptMessage() const
{
    IDocument* pDocument = GetDoc();
    const GraphicsNodeReferenceOwnerPtrVector& curCandidate =
        IPickCandidates::Get()->GetCurrentPick()->GetAllGraphicsNodeReferences();

    if (!pDocument || curCandidate.empty())
        return ActionBase::GetPromptMessage();

    // 显示当前候选项信息（可通过调试模式控制）
    std::wstring msg;
    if (!DEBUG_MODE(GBMPDontPrintCandidateOnStatusBar))
        msg += curCandidate.at(0)->GetDescriptionString();

    return msg;
}
```

## 总结

选择集管理系统通过 CAD 风格选择模式提供了更灵活的交互体验：

1. **模式切换**：通过 `GbmpPickActionUtil::IS_CAD_STYLE_SELECT` 控制选择行为
2. **核心差异**：CAD 模式下无修饰键时累加选择，传统模式下替换选择
3. **框选机制**：CAD 模式需要点击空白后才能框选，传统模式按下即可框选
4. **性能优化**：通过预高亮比较和批量操作减少不必要的视图更新
5. **扩展支持**：提供调试模式和特殊对象（如表格）的处理机制
