# ActionModify 夹点操作详解

## 概述

夹点操作是 ActionModify 中最复杂的交互功能之一，涉及夹点创建、显示、拖拽、修改、事务处理等多个环节。本文档从跨维度的角度详细分析夹点操作的完整工作流程，重点关注与拾取机制、捕捉系统、事件处理的集成，以及与基础夹点系统的协作关系。

> **注意**：本文档重点关注夹点操作的完整流程和跨系统集成。关于夹点系统的基础架构和接口设计，请参考 [05_夹点系统.md](05_夹点系统.md)。

## 夹点操作完整流程

### 1. 夹点操作流程图

```mermaid
flowchart TD
    A[图元选中] --> B[创建夹点]
    B --> C[显示夹点]
    C --> D[用户点击夹点]
    D --> E[开始拖拽]
    E --> F[实时更新]
    F --> G[捕捉处理]
    G --> H[影子对象更新]
    H --> I{继续拖拽?}
    I -->|是| F
    I -->|否| J[结束拖拽]
    J --> K[执行修改]
    K --> L[事务提交]
    L --> M[清理夹点]
    
    N[选择集变化] --> O[清理旧夹点]
    O --> B
    
    P[操作取消] --> Q[事务回滚]
    Q --> M
```

### 2. 夹点操作状态机

```mermaid
stateDiagram-v2
    [*] --> NoSelection : 初始状态
    NoSelection --> GripPointsCreated : 图元选中
    GripPointsCreated --> GripPointSelected : 点击夹点
    GripPointSelected --> Dragging : 开始拖拽
    Dragging --> Dragging : 鼠标移动
    Dragging --> ModifyCompleted : 拖拽结束
    ModifyCompleted --> GripPointsCreated : 修改完成
    
    GripPointsCreated --> NoSelection : 清除选择
    GripPointSelected --> GripPointsCreated : 取消拖拽
    Dragging --> GripPointsCreated : 操作取消
    
    GripPointsCreated --> GripPointsUpdated : 选择集变化
    GripPointsUpdated --> GripPointsCreated : 夹点重建
```

## 夹点创建与管理

### 1. 夹点创建时机

```cpp
// 选择集变化时触发夹点创建
void GbmpActionModify::OnSelectionChanged()
{
    // 1. 清理旧夹点
    ClearExistingGripPoints();
    
    // 2. 获取当前选择集
    const GraphicsNodeReferenceOwnerPtrSet& selections = 
        ISelection::Get()->GetGraphicsNodeReferences();
    
    // 3. 为每个选中图元创建夹点
    for (const auto& selection : selections)
    {
        ElementId elementId = selection->GetElementId();
        CreateGripPointsForElement(elementId);
    }
    
    // 4. 更新视图显示
    UpdateView();
}
```

### 2. 夹点创建实际实现

**注意**: 代码中不存在 `CreateGripPointForElement` 方法，实际的夹点创建实现如下：

```cpp
// 实际的夹点创建方法（以标高视图符号为例）
ElementId CreateGripPoint(ElevationViewSymbolNewAuxiliaryType type,
                         const ElementId& modelviewId,
                         const IElement* pElement)
{
    const IGenericElement* pGenericElement = quick_cast<IGenericElement>(pElement);
    if (!pGenericElement)
        return ElementId::InvalidID;

    // 1. 获取夹点位置
    const IElementPositionPoints* pOwnerPositionPoints = pGenericElement->GetPositionPoints();
    Vector3d pos = pOwnerPositionPoints ?
        pOwnerPositionPoints->GetControlPoint((int)type) : Vector3d::Zero;

    // 2. 创建夹点行为
    OwnerPtr<IElementShapeHandleBehavior> pGripPointBehavior =
        ElevationViewSymbolNewGripPointBehavior::Create(pElement->GetDocument(), modelviewId);

    if (!pGripPointBehavior)
        return ElementId::InvalidID;

    // 3. 创建夹点图元
    IElementShapeHandle* pGripPoint = IElementShapeHandle::Create(
        TransferOwnership(pGripPointBehavior),
        pElement->GetElementId(),     // 主图元ID
        pos,                          // 夹点位置
        (int)type                     // 夹点类型
    );

    return pGripPoint ? pGripPoint->GetElementId() : ElementId::InvalidID;
}
```

**夹点显示限制**:
- **单图元限制**: 系统只能同时显示和操作一个图元的夹点
- **通过 m_auxElementId 管理**: 当前活动的夹点ID存储在 `m_auxElementId` 中
- **选择集变化时清理**: 当选择集变化时，旧的夹点会被清理，新的夹点会被创建

### 3. 夹点行为创建

```cpp
OwnerPtr<IElementShapeHandleBehavior> CreateGripPointBehavior(
    const IElement* pElement,
    const ElementId& modelViewId,
    int positionIndex)
{
    // 根据图元类型创建相应的夹点行为
    if (const IWall* pWall = quick_cast<IWall>(pElement))
    {
        return WallGripPointBehavior::Create(
            pElement->GetDocument(), 
            modelViewId, 
            positionIndex
        );
    }
    else if (const IBeam* pBeam = quick_cast<IBeam>(pElement))
    {
        return BeamGripPointBehavior::Create(
            pElement->GetDocument(), 
            modelViewId, 
            positionIndex
        );
    }
    else if (const IGenericElement* pGeneric = quick_cast<IGenericElement>(pElement))
    {
        // 检查是否为轴网
        if (const SingleGrid* pGrid = quick_cast<SingleGrid>(pGeneric->GetExternalObject()))
        {
            return SingleGridGripPointBehavior::Create(
                pElement->GetDocument(), 
                modelViewId
            );
        }
    }
    
    // 默认夹点行为
    return DefaultGripPointBehavior::Create(
        pElement->GetDocument(), 
        modelViewId, 
        positionIndex
    );
}
```

## 夹点拖拽处理

### 1. 夹点拾取检测

```cpp
bool GbmpActionModify::OnLButtonDown(IUiView* pCurrentView, const Vector3d& pos)
{
    // 1. 调用基类拾取处理
    GbmpPickActionBase::OnLButtonDown(pCurrentView, pos);
    
    // 2. 检查是否拾取到辅助对象（夹点）
    if (m_auxElementId.IsValid())
    {
        IDocument* pDoc = pCurrentView->GetUiDocument()->GetDbDocument();
        IElement* pElement = pDoc->GetElement(m_auxElementId);
        
        if (IElementShapeHandle* pShapeHandle = quick_cast<IElementShapeHandle>(pElement))
        {
            // 3. 验证夹点权限
            if (!pShapeHandle->CanMasterElementToMove())
            {
                ShowPermissionError();
                return false;
            }
            
            // 4. 初始化夹点拖拽
            InitializeGripPointDrag(pShapeHandle, pos);
            
            // 5. 设置特殊捕捉优先级
            SetupGripPointSnapPriority(pShapeHandle);
        }
    }
    
    // 6. 收集移动图元（夹点优先）
    CollectMoveElements(pDoc);
    
    return true;
}
```

### 2. 夹点拖拽初始化

```cpp
void InitializeGripPointDrag(
    IElementShapeHandle* pShapeHandle, 
    const Vector3d& startPos)
{
    // 1. 获取夹点行为
    const IElementShapeHandleBehavior* pBehavior = 
        pShapeHandle->GetElementShapeHandleBehavior();
    
    // 2. 检查是否为夹点集合行为
    const IGripPointsShapeHandleBehavior* pGripBehavior = 
        quick_cast<IGripPointsShapeHandleBehavior>(pBehavior);
    
    if (pGripBehavior)
    {
        // 3. 获取捕捉优先级类型
        std::vector<Int64> priorityTypes = 
            pGripBehavior->GetSnapPriorityTypes();
        
        // 4. 设置捕捉预处理器
        SetSnapCandidatesPreprocessor(
            NEW_AS_OWNER_PTR(GbmpSnapEndPointPriorityTypesPreprocessor, priorityTypes)
        );
    }
    
    // 5. 处理特殊夹点（如裁剪框）
    HandleSpecialGripPoint(pShapeHandle, startPos);
    
    // 6. 初始化捕捉上下文
    m_oSnapContext = ISnapContext::Create();
    InitializeSnapContext(GetCurrentView(), startPos);
}
```

### 3. 夹点移动处理

```cpp
bool GbmpActionModify::OnMovePoint(IUiView* pCurrentView, const Vector3d& pos)
{
    // 1. 基类移动处理
    GbmpPickActionBase::OnMovePoint(pCurrentView, pos);
    
    // 2. 检查是否为夹点拖拽
    if (IsGripPointDragging())
    {
        // 3. 执行捕捉
        SnapPoint(pCurrentView, pos);
        
        // 4. 更新夹点位置
        UpdateGripPointPosition();
        
        // 5. 创建或更新影子对象
        UpdateShadowObjects(pCurrentView);
        
        // 6. 实时反馈
        ProvideRealTimeFeedback(pCurrentView);
        
        return true;
    }
    
    return false;
}

bool IsGripPointDragging() const
{
    return m_auxElementId.IsValid() && 
           (m_status & PS_GBMP_LBUTTON_DOWN) &&
           IsMoving();
}
```

### 4. 夹点影子对象管理

```cpp
void UpdateShadowObjects(IUiView* pCurrentView)
{
    if (!m_opModifyElementsBehavior)
        return;
    
    // 1. 获取主图元ID
    ElementId masterElementId = GetMasterElementId();
    
    // 2. 检查是否已有影子对象
    auto it = m_elementShadows.find(masterElementId);
    if (it == m_elementShadows.end())
    {
        // 3. 创建影子对象
        IElement* pShadowElement = 
            m_opModifyElementsBehavior->CreateElementShadow(
                pCurrentView, masterElementId);
        
        if (pShadowElement)
        {
            m_elementShadows[masterElementId] = pShadowElement->GetElementId();
        }
    }
    else
    {
        // 4. 更新现有影子对象
        UpdateExistingShadowObject(it->second);
    }
}
```

## 夹点修改执行

### 1. 夹点修改提交

```cpp
bool GbmpActionModify::OnLButtonUp(IUiView* pCurrentView, const Vector3d& pos)
{
    // 1. 检查是否为夹点修改
    if (IsGripPointModification())
    {
        return ExecuteGripPointModification(pCurrentView, pos);
    }
    
    // 2. 其他修改处理
    return ExecuteElementModification(pCurrentView, pos);
}

bool ExecuteGripPointModification(IUiView* pCurrentView, const Vector3d& pos)
{
    IDocument* pDoc = pCurrentView->GetUiDocument()->GetDbDocument();
    
    // 1. 创建用户事务
    if (!m_upUserTransaction)
    {
        m_upUserTransaction = IUserTransaction::Create(
            pDoc, GBMP_TR(L"夹点修改"), false);
    }
    
    if (!m_upUserTransaction)
        return false;
    
    // 2. 开始事务
    m_upUserTransaction->Start();
    
    try
    {
        // 3. 执行夹点修改
        bool success = PerformGripPointModification(pCurrentView);
        
        if (success)
        {
            // 4. 处理特殊情况
            HandleSpecialGripPointCases(pCurrentView);
            
            // 5. 后处理
            ProcessGripPointPostModify(pCurrentView);
            
            // 6. 提交事务
            m_upUserTransaction->Commit();
        }
        else
        {
            // 7. 回滚事务
            m_upUserTransaction->Rollback();
        }
        
        return success;
    }
    catch (...)
    {
        // 8. 异常处理
        m_upUserTransaction->Rollback();
        return false;
    }
}
```

### 2. 夹点修改实现

```cpp
bool PerformGripPointModification(IUiView* pCurrentView)
{
    if (!m_opModifyElementsBehavior)
        return false;
    
    // 1. 计算移动向量
    Vector3d moveVector = CalculateGripPointMoveVector();
    
    // 2. 执行夹点修改
    bool success = m_opModifyElementsBehavior->ModifyGripPoint(
        pCurrentView,
        m_auxElementId,      // 夹点ID
        m_startPt,           // 起始点
        moveVector           // 移动向量
    );
    
    if (!success)
        return false;
    
    // 3. 更新夹点位置
    UpdateGripPointAfterModification();
    
    return true;
}

Vector3d CalculateGripPointMoveVector() const
{
    if (IsCaught())
    {
        // 有捕捉时使用捕捉点
        return m_endPt - m_nearStartPt;
    }
    else
    {
        // 无捕捉时使用鼠标位置
        return m_endPt - m_startPt;
    }
}
```

### 3. 夹点行为的修改实现

```cpp
// 以轴网夹点为例
bool SingleGridGripPointBehavior::Translate(const Vector3d& translation)
{
    IElementShapeHandle* pElementShapeHandle = GetElementShapeHandle();
    if (!pElementShapeHandle)
        return false;
    
    // 1. 获取主图元
    ElementId masterId = pElementShapeHandle->GetMasterId();
    IElement* pMasterElement = m_Document->GetElement(masterId);
    
    const IGenericElement* pGenericElement = quick_cast<IGenericElement>(pMasterElement);
    if (!pGenericElement)
        return false;
    
    // 2. 获取轴网对象
    const SingleGrid* pGrid = quick_cast<SingleGrid>(pGenericElement->GetExternalObject());
    if (!pGrid)
        return false;
    
    // 3. 获取控制点类型
    GridControlPointType type = static_cast<GridControlPointType>(
        pElementShapeHandle->GetPositionIndexInMasterElement());
    
    // 4. 计算新位置
    Vector3d currentPos = pElementShapeHandle->GetPosition();
    Vector3d newPos = currentPos + translation;
    
    // 5. 更新轴网控制点
    bool success = UpdateGridControlPoint(pGrid, type, newPos);
    
    if (success)
    {
        // 6. 更新夹点位置
        pElementShapeHandle->SetPosition(newPos);
    }
    
    return success;
}
```

## 夹点与捕捉系统集成

### 1. 夹点捕捉优先级

```cpp
void SetupGripPointSnapPriority(IElementShapeHandle* pShapeHandle)
{
    const IElementShapeHandleBehavior* pBehavior = 
        pShapeHandle->GetElementShapeHandleBehavior();
    
    const IGripPointsShapeHandleBehavior* pGripBehavior = 
        quick_cast<IGripPointsShapeHandleBehavior>(pBehavior);
    
    if (pGripBehavior)
    {
        // 获取捕捉优先级类型
        std::vector<Int64> priorityTypes = 
            pGripBehavior->GetSnapPriorityTypes();
        
        // 设置捕捉预处理器
        OwnerPtr<GbmpSnapEndPointPriorityTypesPreprocessor> pPreprocessor = 
            NEW_AS_OWNER_PTR(GbmpSnapEndPointPriorityTypesPreprocessor, priorityTypes);
        
        SetSnapCandidatesPreprocessor(TransferOwnership(pPreprocessor));
    }
}
```

### 2. 夹点捕捉过滤

```cpp
class GripPointSnapFilter : public IPickFilter
{
public:
    virtual bool AllowElement(const ElementId& elementId) const override
    {
        IDocument* pDoc = GetDocument();
        IElement* pElement = pDoc->GetElement(elementId);
        
        // 1. 不允许捕捉到夹点本身
        if (quick_cast<IElementShapeHandle>(pElement))
            return false;
        
        // 2. 不允许捕捉到正在修改的图元
        if (elementId == m_modifyingElementId)
            return false;
        
        // 3. 其他过滤逻辑
        return IsElementAllowedForGripSnap(pElement);
    }
    
private:
    ElementId m_modifyingElementId;
};
```

## 夹点特殊处理

### 1. 表格夹点处理

```cpp
void HandleTableGripPoint(IUiView* pCurrentView)
{
    IDocument* pDoc = pCurrentView->GetUiDocument()->GetDbDocument();
    
    // 1. 记录表格项索引
    RecordTableItemIndex(pDoc);
    
    // 2. 执行夹点修改
    bool success = m_opModifyElementsBehavior->ModifyGripPoint(
        pCurrentView,
        m_auxElementId,
        m_startPt,
        m_endPt - (IsCaught() ? m_nearStartPt : m_startPt)
    );
    
    if (success)
    {
        // 3. 更新其他页面索引
        if (!g_otherPageIndexs.empty())
        {
            UpdateSelectionOrHeight(pDoc);
        }
    }
}

void RecordTableItemIndex(IDocument* pDoc)
{
    // 记录表格单元格索引，用于后续更新
    const GraphicsNodeReferenceOwnerPtrSet& selections = 
        ISelection::Get()->GetGraphicsNodeReferences();
    
    for (const auto& selection : selections)
    {
        // 提取表格项索引信息
        TableItemIndex index = ExtractTableItemIndex(selection.get());
        if (index.IsValid())
        {
            g_otherPageIndexs.insert(index);
        }
    }
}
```

### 2. 裁剪框夹点处理

```cpp
void HandleViewClipRangeGripPoint(
    IElementShapeHandle* pShapeHandle, 
    Vector3d& position)
{
    IDocument* pDoc = GetDocument();
    
    // 获取裁剪框对象
    const IViewClipRange* pViewClipRange = 
        quick_cast<IViewClipRange>(pDoc->GetElement(pShapeHandle->GetMasterId()));
    
    if (pViewClipRange && pViewClipRange->GetSplitType() != EnSplitType::Unknown)
    {
        // 需要进行子视图坐标映射
        IUiView* pCurrentView = GetCurrentView();
        position = pCurrentView->GetCanvas()->MapFromSubView(position);
    }
}
```

## 夹点生命周期管理

### 1. 夹点清理机制

```cpp
void GbmpActionModify::Reset()
{
    // 1. 清理基础状态
    m_startPt = m_endPt = m_interPt = m_nearStartPt = Vector3d(0, 0, 0);
    m_isCaught = false;
    m_status = PS_GBMP_NOTSTART;
    
    // 2. 清理移动图元列表
    m_moveElementIds.clear();
    
    // 3. 清理影子对象
    ClearShadows();
    
    // 4. 清理夹点相关数据
    ClearGripPointData();
    
    // 5. 清理捕捉渲染数据
    ISnapRender::Get()->ClearSnapRenderData();
}

void ClearGripPointData()
{
    // 清理夹点ID
    m_auxElementId = ElementId::InvalidID;
    
    // 清理捕捉预处理器
    SetSnapCandidatesPreprocessor(nullptr);
    
    // 清理特殊夹点数据
    g_otherPageIndexs.clear();
}
```

### 2. 夹点自动更新

```cpp
void UpdateGripPointsAfterModification()
{
    // 1. 获取当前选择集
    const GraphicsNodeReferenceOwnerPtrSet& selections = 
        ISelection::Get()->GetGraphicsNodeReferences();
    
    // 2. 更新每个图元的夹点
    for (const auto& selection : selections)
    {
        ElementId elementId = selection->GetElementId();
        UpdateGripPointsForElement(elementId);
    }
}

void UpdateGripPointsForElement(const ElementId& elementId)
{
    IDocument* pDoc = GetDocument();
    IElement* pElement = pDoc->GetElement(elementId);
    
    if (!pElement)
        return;
    
    // 1. 获取图元的夹点
    std::vector<ElementId> gripPointIds = GetGripPointsForElement(elementId);
    
    // 2. 更新每个夹点的位置
    for (const ElementId& gripId : gripPointIds)
    {
        IElementShapeHandle* pGripPoint = 
            quick_cast<IElementShapeHandle>(pDoc->GetElement(gripId));
        
        if (pGripPoint)
        {
            // 3. 通过行为更新夹点位置
            IElementShapeHandleBehavior* pBehavior = 
                pGripPoint->GetElementShapeHandleBehavior();
            
            if (pBehavior)
            {
                pBehavior->UpdatePosition();
            }
        }
    }
}
```

## 夹点操作实际实现

**注意**: 代码中不存在 `LazyGripPointManager` 和 `GripPointCache` 类，实际的夹点管理机制如下：

### 1. 夹点管理机制

```cpp
// 实际的夹点管理通过 GbmpActionModify 中的成员变量实现
class GbmpActionModify
{
private:
    ElementId m_auxElementId;                    // 当前活动的辅助图元ID（包括夹点）
    std::vector<ElementId> m_moveElementIds;     // 待移动的图元ID列表

public:
    // 收集移动图元（包括夹点）
    void CollectMoveElements(IDocument* pDoc)
    {
        m_moveElementIds.clear();

        if (m_auxElementId.IsValid())
        {
            // 如果有夹点被选中，优先处理夹点
            m_moveElementIds.push_back(m_auxElementId);
        }
        else
        {
            // 否则收集选择集中的图元
            const GraphicsNodeReferenceOwnerPtrSet& selections =
                ISelection::Get()->GetGraphicsNodeReferences();

            for (const auto& selection : selections)
            {
                m_moveElementIds.push_back(selection->GetElementId());
            }
        }
    }

    // 重置时清理夹点数据
    void Reset()
    {
        m_moveElementIds.clear();
        // 其他清理操作...
    }
};
```

### 2. 夹点创建的实际流程

```cpp
// 夹点创建通过具体的UI操作策略实现
// 例如：ElevationViewSymbolNewUiManipulateStrategy
ElementId CreateGripPoint(ElevationViewSymbolNewAuxiliaryType type,
                         const ElementId& modelviewId,
                         const IElement* pElement)
{
    // 1. 获取图元的定位点组件
    const IElementPositionPoints* pOwnerPositionPoints =
        pElement->GetPositionPoints();

    // 2. 计算夹点位置
    Vector3d pos = pOwnerPositionPoints ?
        pOwnerPositionPoints->GetControlPoint((int)type) : Vector3d::Zero;

    // 3. 创建夹点行为
    OwnerPtr<IElementShapeHandleBehavior> pGripPointBehavior =
        ElevationViewSymbolNewGripPointBehavior::Create(
            pElement->GetDocument(), modelviewId);

    // 4. 创建夹点图元
    IElementShapeHandle* pGripPoint = IElementShapeHandle::Create(
        TransferOwnership(pGripPointBehavior),
        pElement->GetElementId(),
        pos,
        (int)type
    );

    return pGripPoint ? pGripPoint->GetElementId() : ElementId::InvalidID;
}
```

## 夹点操作调试支持

### 1. 夹点调试模式

```cpp
CREATE_DEBUG_MODE(CtlrDontNeedTableItemGripPoints, 
    L"Ctrl选择不需要生成单元格夹点", 
    gcmp::DebugModeGroup::DMGT_GREP_AND_PICKSNAP, 
    L"GDMPLab", L"2023-12-20");

CREATE_DEBUG_MODE(ShowGripPointDebugInfo, 
    L"显示夹点调试信息", 
    gcmp::DebugModeGroup::DMGT_GREP_AND_PICKSNAP, 
    L"GDMPLab", L"2024-01-15");
```

### 2. 夹点操作日志

```cpp
void LogGripPointOperation(
    const std::wstring& operation,
    const ElementId& gripPointId,
    const ElementId& masterElementId,
    const Vector3d& position)
{
    if (IsDebugModeEnabled(ShowGripPointDebugInfo))
    {
        DBG_INFO(L"GripPoint %s: GripID=%s, MasterID=%s, Pos=(%.2f,%.2f,%.2f)",
                 operation.c_str(),
                 gripPointId.ToString().c_str(),
                 masterElementId.ToString().c_str(),
                 position.X, position.Y, position.Z);
    }
}
```
