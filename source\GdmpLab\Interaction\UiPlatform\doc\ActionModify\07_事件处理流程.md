# ActionModify 事件处理流程

## 概述

ActionModify 的事件处理流程是整个交互系统的核心，负责处理用户的鼠标和键盘输入，并将这些事件转换为具体的图元修改操作。事件处理采用分层架构，从基类到派生类逐层处理，支持事件拦截和自定义处理。

## 事件处理架构

### 1. 事件处理层次结构

```mermaid
graph TD
    A[用户输入事件] --> B[ActionBase 基础处理]
    B --> C[GbmpPickActionBase 拾取处理]
    C --> D[GbmpActionModify 修改处理]
    D --> E[IActionModifyBehavior 行为处理]
    
    F[鼠标事件] --> A
    G[键盘事件] --> A
    H[视图事件] --> A
    
    I[OnLButtonDown] --> F
    J[OnLButtonUp] --> F
    K[OnMovePoint] --> F
    L[OnRButtonDown] --> F
    M[OnRButtonUp] --> F
    N[OnLButtonDoubleClick] --> F
    
    O[OnKeyDown] --> G
    P[OnKeyUp] --> G
    
    Q[OnViewSwitched] --> H
```

### 2. 事件处理状态机

```mermaid
stateDiagram-v2
    [*] --> NotStarted : InitAction
    NotStarted --> LButtonDown : OnLButtonDown
    LButtonDown --> Moving : OnMovePoint(distance > tolerance)
    LButtonDown --> LButtonUp : OnLButtonUp(no move)
    Moving --> LButtonUp : OnLButtonUp
    LButtonUp --> NotStarted : Reset
    
    LButtonDown --> Cancelled : OnRButtonDown/OnKeyDown(ESC)
    Moving --> Cancelled : OnRButtonDown/OnKeyDown(ESC)
    Cancelled --> [*] : ActionCancelled
    
    NotStarted --> DoubleClick : OnLButtonDoubleClick
    DoubleClick --> NotStarted : Reset
```

## 鼠标事件处理

### 1. OnLButtonDown 左键按下

```cpp
bool GbmpActionModify::OnLButtonDown(IUiView* pCurrentView, const Vector3d& pos)
{
    // 1. 调用基类处理（拾取逻辑）
    GbmpPickActionBase::OnLButtonDown(pCurrentView, pos);
    
    // 2. 计算工作平面交点
    Vector3d posOnWP;
    IDocument* pDoc = pCurrentView->GetUiDocument()->GetDbDocument();
    
    // 3. 收集要移动的图元
    CollectMoveElements(pDoc);
    
    if (ActionBase::ProjectPositionOnWorkPlane(pCurrentView, pos, posOnWP))
    {
        // 4. 处理辅助对象（夹点）
        if (m_auxElementId != ElementId::InvalidID)
        {
            const IElementShapeHandle* pShapeHandle = 
                quick_cast<IElementShapeHandle>(pDoc->GetElement(m_auxElementId));
            
            if (pShapeHandle)
            {
                // 特殊处理：裁剪框夹点坐标转换
                const IViewClipRange* pViewClipRange = 
                    quick_cast<IViewClipRange>(pDoc->GetElement(pShapeHandle->GetMasterId()));
                
                if (pViewClipRange && pViewClipRange->GetSplitType() != EnSplitType::Unknown)
                {
                    posOnWP = pCurrentView->GetCanvas()->MapFromSubView(posOnWP);
                }
            }
        }
        
        // 5. 设置起始点
        m_startPt = posOnWP;
        m_status = m_status | PS_GBMP_LBUTTON_DOWN;
    }
    
    // 6. 初始化捕捉上下文
    m_oSnapContext = ISnapContext::Create();
    GetNearestPoint(pCurrentView, m_startPt, m_nearStartPt);
    
    // 7. 设置捕捉过滤器
    SetupSnapFilters();
    
    // 8. 初始化捕捉上下文
    InitializeSnapContext(pCurrentView, pos);
    
    // 9. 显示元素定位器
    DisplayElementLocator(pDoc, pCurrentView, false);
    
    return true;
}
```

### 2. OnLButtonUp 左键抬起

```cpp
bool GbmpActionModify::OnLButtonUp(IUiView* pCurrentView, const Vector3d& pos)
{
    // 1. 调用基类处理
    GbmpPickActionBase::OnLButtonUp(pCurrentView, pos);
    
    IDocument* pDoc = pCurrentView->GetUiDocument()->GetDbDocument();
    
    // 2. 显示元素定位器
    DisplayElementLocator(pDoc, pCurrentView);
    
    // 3. 检查是否有实际移动
    if (Vector3dUtils::Distance(m_startPt, m_endPt) > 1)
    {
        // 4. 清除阴影对象
        bool cleared = ClearShadows();
        
        if (cleared)
        {
            // 5. 创建用户事务
            if (m_upUserTransaction == nullptr)
            {
                m_upUserTransaction = IUserTransaction::Create(
                    pDoc, GBMP_TR(L"移动"), false);
            }
            m_upUserTransaction->Start();
            
            // 6. 重新收集移动图元（辅助对象已无效）
            CollectMoveElements(pDoc);
            
            // 7. 执行图元修改
            bool isOk = true;
            for (int loop = 0; loop < (int)m_moveElementIds.size(); ++loop)
            {
                Vector3d moveVector = m_endPt - (IsCaught() ? m_nearStartPt : m_startPt);
                isOk = m_opModifyElementsBehavior->ModifyElement(
                    pCurrentView, 
                    m_moveElementIds[loop], 
                    m_startPt, 
                    moveVector
                );
                
                if (!isOk)
                {
                    Reset();
                    break;
                }
            }
            
            // 8. 处理特殊情况（表格、共线捕捉等）
            HandleSpecialCases(pCurrentView);
            
            // 9. 后处理
            if (isOk)
            {
                m_opModifyElementsBehavior->ProcessPostModify(
                    pCurrentView, 
                    m_moveElementIds, 
                    m_oSnapCandidates.get()
                );
                m_upUserTransaction->Commit();
            }
            else
            {
                m_upUserTransaction->Rollback();
            }
        }
    }
    
    // 10. 重置状态
    Reset();
    return true;
}
```

### 3. OnMovePoint 鼠标移动

```cpp
bool GbmpActionModify::OnMovePoint(IUiView* pCurrentView, const Vector3d& pos)
{
    // 1. 调用基类处理
    GbmpPickActionBase::OnMovePoint(pCurrentView, pos);
    
    // 2. 检查是否允许拖拽选中图元
    if ((m_status & PS_GBMP_LBUTTON_DOWN) &&
        IsDraggingAwaySelectedElementsEnabled(pCurrentView))
    {
        m_moveElementIds.clear();
    }
    
    // 3. 检查移动条件
    if ((m_status & PS_GBMP_LBUTTON_DOWN) &&
        ISelection::Get()->GetCount() > 0 &&
        !IsKeyAndButtonPressed(VK_CONTROL) &&
        !IsKeyAndButtonPressed(VK_SHIFT) &&
        IsAllowedMove())
    {
        // 4. 表格特殊处理
        if (HandleTableSpecialCase())
            return true;
        
        // 5. 执行捕捉
        SnapPoint(pCurrentView, pos);
        
        // 6. 递归调用移动处理
        return OnMovePoint(pCurrentView);
    }
    
    // 7. 计算移动容差
    int moveTolerancePixel = 10; // pixels
    double moveTolerance = UiViewUtility::ComputeWorldWidthFromPixel(
        pCurrentView, moveTolerancePixel);
    
    // 8. 更新移动状态
    if (!IsMoving() && Vector3dUtils::Distance(m_endPt, m_startPt) > moveTolerance)
        m_status = m_status | PS_GBMP_LBUTTON_MOVING;
    
    if (!IsMoving())
        return true;
    
    // 9. 处理立面视图捕捉点转换
    Vector3d nearStartPt = HandleElevationViewSnap(pCurrentView);
    
    // 10. 执行阴影对象移动
    ExecuteShadowMove(pCurrentView, nearStartPt);
    
    return true;
}
```

### 4. OnLButtonDoubleClick 双击事件

```cpp
bool GbmpActionModify::OnLButtonDoubleClick(IUiView* pCurrentView, const Vector3d& pos)
{
    // 1. 调用基类处理
    GbmpPickActionBase::OnLButtonDoubleClick(pCurrentView, pos);
    
    // 2. 获取选择集
    const GraphicsNodeReferenceOwnerPtrSet& gnodeRefs = 
        ISelection::Get()->GetGraphicsNodeReferences();
    
    if (gnodeRefs.empty())
        return true;
    
    // 3. 检查表格特殊处理
    IDrawingTable* pTableElement = nullptr;
    if (gnodeRefs.size() > 1)
    {
        // 检查是否为表格元素
        ElementId elementId = (*gnodeRefs.begin())->GetElementId();
        IElement* pElement = GetDoc()->GetElement(elementId);
        const IGenericElement* pGenericElement = quick_cast<IGenericElement>(pElement);
        if (pGenericElement)
            pTableElement = quick_cast<IDrawingTable>(pGenericElement->GetExternalObject());
    }
    
    // 4. 处理双击事件
    if (gnodeRefs.size() == 1 || (gnodeRefs.size() > 1 && pTableElement))
    {
        if (m_opModifyElementsBehavior)
        {
            ElementId elementId = (*gnodeRefs.begin())->GetElementId();
            bool res = m_opModifyElementsBehavior->OnLButtonDoubleClick(
                pCurrentView, pos, elementId);
            
            if (false == res)
                return res;
        }
        
        // 5. 重置状态
        Reset();
        UpdateView();
    }
    
    return true;
}
```

### 5. OnRButtonDown/OnRButtonUp 右键事件

```cpp
bool GbmpActionModify::OnRButtonDown(IUiView* pCurrentView, const Vector3d& pos)
{
    // 右键按下事件处理
    return true;
}

bool GbmpActionModify::OnRButtonUp(IUiView* pCurrentView, const Vector3d& pos)
{
    // 检查子操作结束原因
    ActionFinishReason childActionCancelReason = GetChildFinishReson();
    if (childActionCancelReason == ActionFinishReason::RButtonDown ||
        childActionCancelReason == ActionFinishReason::RButtonUp)
    {
        // 子操作右键结束，不处理
        return false;
    }
    
    // 其他右键处理逻辑
    return true;
}
```

## 键盘事件处理

### 1. OnKeyDown 键盘按下

```cpp
bool GbmpActionModify::OnKeyDown(IUiView* pCurrentView, int nChar)
{
    // 1. 处理修饰键
    if (IsKeyAndButtonPressed(VK_CONTROL))
    {
        SetCursorType(CursorType::ArrowCursorWithPlus);
    }
    else if (IsKeyAndButtonPressed(VK_SHIFT))
    {
        SetCursorType(CursorType::ArrowCursorWithMinus);
    }
    
    // 2. 调用基类处理
    if (GbmpPickActionBase::OnKeyDown(pCurrentView, nChar))
        return true;
    
    // 3. Tab键处理（捕捉候选切换）
    if (nChar == 9)  // Tab键
    {
        if (nullptr == m_oSnapCandidates)
            return false;
            
        const std::vector<const ISnap*>& snapResults = 
            m_oSnapCandidates->GetCandidates();
        size_t numResults = snapResults.size();
        
        if (numResults > 1)
        {
            // 切换到下一个捕捉候选
            const ISnap* pSnap = m_oSnapCandidates->UpdateCurrentSnapToNext();
            
            m_isCaught = true;
            SetPromptMessage(pSnap->GetLongPrompt());
            m_endPt = pSnap->GetSnappedPoint();
            
            // 重新渲染
            ISnapRender::Get()->Draw(pSnap, SnapTextPositionType::LowerRight);
            OnMovePoint(pCurrentView);
            
            return true;
        }
    }
    
    return false;
}
```

### 2. OnKeyUp 键盘抬起

```cpp
bool GbmpActionModify::OnKeyUp(IUiView* pCurrentView, int nChar)
{
    // 1. 恢复光标类型
    CursorType cursorType = GetCursorType();
    if (cursorType == CursorType::ArrowCursorWithPlus ||
        cursorType == CursorType::ArrowCursorWithMinus)
    {
        SetCursorType(CursorType::ArrowCursor);
    }
    
    // 2. 调用基类处理
    return GbmpPickActionBase::OnKeyUp(pCurrentView, nChar);
}
```

## 事件处理状态管理

### 1. 状态定义

```cpp
enum PickStatus
{
    PS_GBMP_NOTSTART = 0x00,           // 未开始
    PS_GBMP_LBUTTON_DOWN = 0x01,       // 左键按下
    PS_GBMP_LBUTTON_UP = 0x02,         // 左键抬起
    PS_GBMP_LBUTTON_MOVING = 0x04,     // 正在移动
    PS_GBMP_RBUTTON_DOWN = 0x08,       // 右键按下
    PS_GBMP_RBUTTON_UP = 0x10          // 右键抬起
};
```

### 2. 状态检查方法

```cpp
// 检查是否正在移动
bool IsMoving() const
{
    return (m_status & PS_GBMP_LBUTTON_MOVING) != 0;
}

// 检查左键是否按下
bool IsLButtonDown() const
{
    return (m_status & PS_GBMP_LBUTTON_DOWN) != 0;
}

// 检查是否允许移动
bool IsAllowedMove() const
{
    // 根据当前状态和条件判断
    return true; // 具体实现逻辑
}
```

### 3. 状态重置

```cpp
void Reset()
{
    // 重置坐标点
    m_startPt = m_endPt = m_interPt = m_nearStartPt = Vector3d(0, 0, 0);
    
    // 重置状态标志
    m_isCaught = false;
    m_status = PS_GBMP_NOTSTART;
    
    // 清除移动图元列表
    m_moveElementIds.clear();
    
    // 清除阴影对象
    ClearShadows();
    
    // 清除捕捉渲染数据
    ISnapRender::Get()->ClearSnapRenderData();
}
```

## 事件取消处理

### 1. ActionCancelled 操作取消

```cpp
void GbmpActionModify::ActionCancelled()
{
    // 1. 调用基类处理
    GbmpPickActionBase::ActionCancelled();
    
    // 2. 回滚事务
    if (m_upUserTransaction != nullptr && m_upUserTransaction->IsStarted())
    {
        IDocument* pDoc = GetDoc();
        m_upUserTransaction->Rollback();
        
        // 清除选择集和高亮
        ISelection::Get()->Clear(pDoc);
        IHighlights::Get()->Clear();
        OnSelectionChanged();
        IPickCandidates::Get()->Clear();
    }
    
    // 3. 清理临时图形
    CleanupTempGraphicsShape();
    
    // 4. 重置状态
    Reset();
    
    // 5. 标记完成状态
    MarkFinishStatus(ActionFinishStatus::Cancelled);
}
```

## 特殊事件处理

### 1. 表格特殊处理

```cpp
bool HandleTableSpecialCase()
{
    if (!m_moveElementIds.empty())
    {
        IDocument* pDoc = GetDoc();
        if (pDoc)
        {
            for (const ElementId& elementId : m_moveElementIds)
            {
                IElement* pElement = pDoc->GetElement(elementId);
                if (!pElement)
                    continue;
                
                IElementBasicInformation* pBasicInfo = pElement->GetBasicInformation();
                if (pBasicInfo)
                {
                    // 检查是否为表格相关对象
                    if (pBasicInfo->GetCategoryUid() == 
                        BuiltInCategoryUniIdentities::BICU_DRAWING_TABLE_ELEMENT ||
                        pBasicInfo->GetClassficationUid() == 
                        GuidUtils::FromString(L"{EDBC1DCC-7BC6-4EE1-BD90-0880B2E3E5C8}"))
                    {
                        // 清除移动图元和选择集
                        m_moveElementIds.clear();
                        ISelection::Get()->Clear(pDoc);
                        IHighlights::Get()->Clear();
                        IPreHighlights::Get()->Clear();
                        return true;
                    }
                }
            }
        }
    }
    return false;
}
```

### 2. 立面视图捕捉处理

```cpp
Vector3d HandleElevationViewSnap(IUiView* pCurrentView)
{
    Vector3d nearStartPt = m_nearStartPt;
    
    // 在立面视图有捕捉点的情况下转换到平面空间
    IModelView* pModelView = pCurrentView->GetModelView();
    if (IsCaught() && 
        Vector3dUtils::IsPerpendicular(pModelView->GetViewDirection(), Vector3d::UnitZ))
    {
        OwnerPtr<IPlane> workPlane = pModelView->GetWorkPlane();
        if (workPlane)
        {
            Vector2d uvParameter;
            AlgorithmProject::Project(m_nearStartPt, workPlane.get(), uvParameter, nearStartPt);
        }
    }
    
    return nearStartPt;
}
```

## 事件处理流程图

```mermaid
flowchart TD
    A[用户输入] --> B{事件类型}
    
    B -->|鼠标左键按下| C[OnLButtonDown]
    B -->|鼠标左键抬起| D[OnLButtonUp]
    B -->|鼠标移动| E[OnMovePoint]
    B -->|鼠标双击| F[OnLButtonDoubleClick]
    B -->|鼠标右键| G[OnRButtonDown/Up]
    B -->|键盘按下| H[OnKeyDown]
    B -->|键盘抬起| I[OnKeyUp]
    
    C --> C1[基类处理]
    C1 --> C2[收集移动图元]
    C2 --> C3[初始化捕捉]
    C3 --> C4[设置状态]
    
    D --> D1[检查移动距离]
    D1 --> D2{是否移动}
    D2 -->|是| D3[执行修改]
    D2 -->|否| D4[重置状态]
    D3 --> D5[提交事务]
    
    E --> E1[检查移动条件]
    E1 --> E2{允许移动}
    E2 -->|是| E3[执行捕捉]
    E2 -->|否| E4[返回]
    E3 --> E5[更新阴影]
    
    F --> F1[获取选择集]
    F1 --> F2[委托行为处理]
    F2 --> F3[重置状态]
    
    H --> H1[处理修饰键]
    H1 --> H2[Tab键切换捕捉]
    H2 --> H3[基类处理]
```
