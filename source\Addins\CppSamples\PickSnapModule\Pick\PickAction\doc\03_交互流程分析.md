# 拾取Action交互流程分析

## 概述

本文档分析GDMPLab中拾取Action的交互流程，包括鼠标事件处理、拾取流程、选择集管理等核心交互机制。拾取Action通过一系列事件驱动的流程，实现用户与三维场景中对象的交互。

## 核心交互流程

### 1. Action生命周期流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant ActionMgr as Action管理器
    participant PickAction as 拾取Action
    participant UI as UI系统
    participant Selection as 选择集
    
    User->>ActionMgr: 启动拾取命令
    ActionMgr->>PickAction: 创建Action实例
    PickAction->>PickAction: InitAction()
    PickAction->>UI: 初始化UI行为
    
    loop 交互循环
        User->>PickAction: 鼠标/键盘事件
        PickAction->>PickAction: 处理事件
        PickAction->>Selection: 更新选择集
        PickAction->>UI: 更新视觉反馈
    end
    
    PickAction->>UI: 清理UI
    PickAction->>ActionMgr: 返回结果
    ActionMgr->>User: 完成操作
```

### 2. 鼠标事件处理流程

#### 鼠标左键按下 (OnLButtonDown)
```cpp
bool SamplePickNodeReferenceAction::OnLButtonDown(IUiView* pCurrentView, const Vector3d& pos)
{
    // 1. 获取当前鼠标位置
    GetCurrentMousePosition(pCurrentView, pos);
    
    // 2. 调用基类处理拾取逻辑
    if (!SamplePickActionBase::OnLButtonDown(pCurrentView, pos))
    {
        // 3. 处理特殊情况
        if (m_bPickElementShapeHandle && m_auxElementId.IsValid())
        {
            // 拾取到辅助对象
            *(m_pned.pPickedAuxiliaryElementId) = m_auxElementId;
            MarkFinishStatus(ActionFinishStatus::Successful);
            return true;
        }
        
        if (m_isPickNullReturnPos)
        {
            // 没有拾取到对象但需要返回位置
            Vector3d posOnWP;
            if (ProjectPositionOnWorkPlane(pCurrentView, pos, posOnWP))
                *m_pickedPos = posOnWP;
            MarkFinishStatus(ActionFinishStatus::Successful);
            return true;
        }
        
        return false; // 继续拾取
    }
    
    // 4. 根据选择模式决定是否结束
    if (!m_bMultiSelectMode)
    {
        SAFE_INIT_POINTER(m_pned.pUserCancelled, false);
        MarkFinishStatus(ActionFinishStatus::Successful);
    }
    
    return true;
}
```

#### 鼠标移动 (OnMovePoint)
```cpp
bool SamplePickActionBase::OnMovePoint(IUiView* pCurrentView, const Vector3d& pos)
{
    // 1. 更新鼠标位置
    GetCurrentMousePosition(pCurrentView, pos);
    
    // 2. 更新拾取候选项
    if (IsAllowedMove())
    {
        UpdateCandidates(pCurrentView, pos);
    }
    
    // 3. 处理框选矩形绘制
    if (m_status & (unsigned int)GcmpEnPickStatus::PS_LBUTTON_DOWN)
    {
        DrawSelectRectangle(pCurrentView, pos, false);
    }
    
    // 4. 调用移动回调
    if (m_moveCallback)
    {
        m_moveCallback(pCurrentView, pos);
    }
    
    return true;
}
```

### 3. 拾取候选项更新流程

```mermaid
graph TD
    A[鼠标移动] --> B[UpdateCandidates]
    B --> C[PickActionUtil::UpdateCandidatesSingleton]
    C --> D[获取拾取器IPicker]
    D --> E[创建拾取上下文]
    E --> F[应用拾取过滤器]
    F --> G[执行拾取操作]
    G --> H[获取拾取结果]
    H --> I[更新预高亮]
    I --> J[更新状态栏信息]
```

#### 候选项更新核心逻辑
```cpp
void PickActionUtil::UpdateCandidatesSingleton(
    IUiView* pCurrentView,
    int screenX, int screenY,
    const Vector3d& pos,
    const IPickEvent* pPickPostProcesserEvent,
    const IPickFilter* pPickFilter,
    const IPickTarget* pPickTarget,
    bool isPickingHighlightOnlyGraphicsNodeAllowed,
    bool selectByFaceInteriorEnabled,
    int pickTolerance,
    bool isHoveringHighlight)
{
    // 1. 获取文档和拾取器
    IUiDocument* pUiDocument = pCurrentView->GetUiDocument();
    IDocument* pDocument = pUiDocument->GetDocument();
    IPicker* pPicker = IPicker::Get();
    
    // 2. 创建拾取上下文
    OwnerPtr<IPointPickContext> opPointPickContext = IPointPickContext::Create();
    opPointPickContext->SetScreenPosition(screenX, screenY);
    opPointPickContext->SetWorldPosition(pos);
    
    // 3. 设置拾取参数
    if (pickTolerance > 0)
        opPointPickContext->SetPixelTolerance(pickTolerance);
    
    // 4. 执行拾取
    OwnerPtr<IPickResult> opPickResult = pPicker->Pick(
        pCurrentView, 
        opPointPickContext.get(), 
        pPickTarget, 
        pPickFilter
    );
    
    // 5. 处理拾取结果
    if (opPickResult)
    {
        IPickCandidates* pPickCandidates = IPickCandidates::Get();
        pPickCandidates->SetPickResult(TransferOwnership(opPickResult));
        
        // 6. 执行后处理事件
        if (pPickPostProcesserEvent)
            pPickPostProcesserEvent->Execute(pCurrentView, pos);
    }
}
```

### 4. 选择集管理流程

#### 选择集操作策略
拾取Action支持多种选择集操作模式，通过键盘修饰键控制：

```cpp
void SamplePickActionBase::AddToSelection(IDocument* pDocument, const IGraphicsNodeReference& pickResult)
{
    bool ctrlKeyPressed = IsKeyAndButtonPressed(VK_CONTROL);
    bool shiftKeyPressed = IsKeyAndButtonPressed(VK_SHIFT);
    ISelection* pGlobalSelection = ISelection::Get();
    
    // 1. Ctrl键：总是添加到选择集
    if (ctrlKeyPressed && !shiftKeyPressed)
    {
        pGlobalSelection->AddGraphicsNodeReference(pDocument, pickResult);
    }
    
    // 2. Shift键：总是从选择集中移除
    if (!ctrlKeyPressed && shiftKeyPressed)
    {
        pGlobalSelection->DeleteGraphicsNodeReference(pDocument, pickResult);
    }
    
    // 3. 无修饰键：替换选择集
    if (!ctrlKeyPressed && !shiftKeyPressed)
    {
        GraphicsNodeReferenceOwnerPtrSet nodeRefs;
        nodeRefs.insert(TransferOwnership(pickResult.Clone()));
        pGlobalSelection->ReplaceGroupGraphicsNodeReference(pDocument, nodeRefs);
    }
    
    // 4. Ctrl+Shift：反转选择状态
    if (ctrlKeyPressed && shiftKeyPressed)
    {
        GraphicsNodeReferenceOwnerPtrSet nodeRefs;
        nodeRefs.insert(TransferOwnership(pickResult.Clone()));
        pGlobalSelection->RevertGroupGraphicsNodeReference(pDocument, nodeRefs);
    }
    
    // 5. 更新高亮显示
    UpdateHighlightOnlySelection();
}
```

#### 选择集状态图
```mermaid
stateDiagram-v2
    [*] --> Empty: 初始状态
    Empty --> Single: 点击对象
    Single --> Multiple: Ctrl+点击其他对象
    Single --> Empty: 点击空白区域
    Single --> Different: 点击其他对象
    Multiple --> Multiple: Ctrl+点击对象(添加/移除)
    Multiple --> Single: 点击单个对象
    Multiple --> Empty: 点击空白区域
    Different --> Single: 替换选择
```

### 5. 框选操作流程

#### 框选状态管理
```cpp
enum class GcmpEnPickStatus : int32_t
{
    PS_NOTSTART = 0,        // 未开始
    PS_LBUTTON_DOWN = 1,    // 左键按下
    PS_LBUTTON_UP = 2,      // 左键释放
    PS_LBUTTON_MOVING = 4,  // 左键拖拽中
    PS_LBUTTON_DBCLK = 8    // 左键双击
};
```

#### 框选矩形绘制
```cpp
void SamplePickActionBase::DrawSelectRectangle(IUiView* pCurrentView, const Vector3d& pos, bool isIntersect)
{
    // 1. 创建选择矩形图形表达
    OwnerPtr<IGraphicsElementShape> opGrep = CreatSelectRectangleGrep(pCurrentView, pos);
    
    // 2. 清理之前的矩形
    if (m_rectangleSelectID.IsValid())
    {
        IPureGraphicsElement* pOldElement = quick_cast<IPureGraphicsElement>(pDoc->GetElement(m_rectangleSelectID));
        if (pOldElement)
            pDoc->DeleteElement(m_rectangleSelectID);
    }
    
    // 3. 添加新的矩形到文档
    IPureGraphicsElement* pElement = IPureGraphicsElement::Create(pDoc, ElementCreationOptions::Transient);
    pElement->SetGraphicsElementShape(TransferOwnership(opGrep));
    m_rectangleSelectID = pElement->GetElementId();
    
    // 4. 刷新视图
    UpdateView();
}
```

### 6. 键盘事件处理流程

```cpp
bool SamplePickNodeReferenceAction::OnKeyDown(IUiView* pCurrentView, int nChar)
{
    // 1. 执行自定义键盘回调
    if (m_OnKeyDownCallback)
    {
        Vector3d mousePt;
        GetCurrentMousePosition(pCurrentView, mousePt);
        if (m_OnKeyDownCallback(nChar, pCurrentView, mousePt))
            return true; // 回调处理了事件
    }
    
    // 2. 处理特殊键
    switch (nChar)
    {
        case VK_ESCAPE:
            // ESC键取消操作
            SAFE_INIT_POINTER(m_pned.pUserCancelled, true);
            MarkFinishStatus(ActionFinishStatus::Cancelled);
            return true;
            
        case VK_RETURN:
        case VK_SPACE:
            // 回车或空格键确认选择
            if (m_bMultiSelectMode)
            {
                SAFE_INIT_POINTER(m_pned.pUserCancelled, false);
                MarkFinishStatus(ActionFinishStatus::Successful);
                return true;
            }
            break;
    }
    
    // 3. 调用基类处理
    return SamplePickActionBase::OnKeyDown(pCurrentView, nChar);
}
```

### 7. 视觉反馈机制

#### 高亮更新流程
```cpp
void SamplePickActionBase::UpdateHighlightOnlySelection()
{
    // 1. 获取当前选择集
    const auto& selectionGNodeRefs = ISelection::Get()->GetGraphicsNodeReferences();
    std::vector<OwnerPtr<IGraphicsNodeReference>> highLightGNodeRefs;
    
    // 2. 转换为高亮集合
    OwnerPtrContainerUtil::AddItems(highLightGNodeRefs, selectionGNodeRefs);
    
    // 3. 清除旧高亮并设置新高亮
    IHighlights::Get()->Clear();
    IHighlights::Get()->AddGraphicsNodeReferences(highLightGNodeRefs);
    
    // 4. 单选时添加关联对象高亮
    if (ISelection::Get()->GetCount() == 1)
    {
        ElementId elementId = selectionGNodeRefs.begin()->get()->GetElementId();
        auto associatedObjects = GetElementAssociatedSelectionObjects(elementId);
        IHighlights::Get()->AddGraphicsNodeReferences(associatedObjects);
    }
}
```

## 交互模式分析

### 1. 单选模式
- **特点**: 点击即选择并结束Action
- **适用场景**: 需要选择单个对象的操作
- **实现**: `m_bMultiSelectMode = false`

### 2. 多选模式
- **特点**: 支持多次选择，需要确认键结束
- **适用场景**: 需要选择多个对象的批量操作
- **UI支持**: 显示应用/取消按钮
- **实现**: `m_bMultiSelectMode = true`

### 3. 框选模式
- **触发**: 鼠标拖拽操作
- **类型**: 
  - 左到右拖拽：完全包含选择
  - 右到左拖拽：相交选择
- **视觉反馈**: 实时绘制选择矩形

## 性能优化机制

### 1. 候选项缓存
- 避免重复计算拾取结果
- 基于鼠标位置的增量更新

### 2. 视图更新优化
- 批量更新高亮显示
- 延迟刷新机制

### 3. 事件过滤
- 早期过滤不符合条件的对象
- 减少不必要的计算开销

## 总结

拾取Action的交互流程设计体现了良好的用户体验和系统性能的平衡。通过事件驱动的架构、灵活的选择策略、丰富的视觉反馈和高效的性能优化，为用户提供了直观、流畅的三维对象交互体验。整个流程的模块化设计也为功能扩展和定制提供了良好的基础。
