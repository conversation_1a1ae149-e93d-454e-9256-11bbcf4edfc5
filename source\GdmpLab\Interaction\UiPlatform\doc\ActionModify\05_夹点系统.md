# ActionModify 夹点系统

## 概述

夹点系统是 ActionModify 中用于图元精确编辑的重要机制。夹点（Grip Point）是显示在选中图元上的可视化控制点，用户可以通过拖拽夹点来精确修改图元的几何形状、位置和属性。

## 核心接口架构

### 1. IElementShapeHandle 辅助对象

夹点在 GDMP 中以辅助对象的形式存在，基于 GCMP SDK 的 IElementShapeHandle 接口：

```cpp
/// \brief 辅助对象（夹点）
class IElementShapeHandle : public IElement
{
public:
    /// \brief 创建辅助对象
    /// \param pBehavior 辅助对象的行为组件
    /// \param masterId 辅助对象绑定的Element的Id
    /// \param position 辅助对象的位置
    /// \param positionIndexInMasterElement 辅助对象的位置索引
    /// \param isDelayRegenHandle 是否延迟关联更新辅助对象
    /// \return IElementShapeHandle* 辅助对象指针
    static IElementShapeHandle* Create(OwnerPtr<IElementShapeHandleBehavior> pBehavior,
                                      const ElementId& masterId,
                                      const Vector3d position = Vector3d(),
                                      int positionIndexInMasterElement = -1,
                                      bool isDelayRegenHandle = false);

    /// \brief 获得辅助对象所绑定的Element的Id
    virtual ElementId GetMasterId() const = 0;

    /// \brief 设置辅助对象所绑定的Element的Id
    virtual void SetMasterId(const ElementId& id) = 0;

    /// \brief 获得辅助对象的位置
    virtual Vector3d GetPosition() const = 0;

    /// \brief 设置辅助对象的位置
    virtual void SetPosition(const Vector3d& pos) = 0;

    /// \brief 获得辅助对象的位置索引
    virtual int GetPositionIndexInMasterElement() const = 0;

    /// \brief 设置辅助对象的位置索引
    virtual void SetPositionIndexInMasterElement(int positionIndexInMasterElement) = 0;

    /// \brief 获得辅助对象的行为组件
    virtual IElementShapeHandleBehavior* GetElementShapeHandleBehavior() = 0;

    /// \brief 设置辅助对象的行为组件
    virtual void SetElementShapeHandleBehavior(OwnerPtr<IElementShapeHandleBehavior> pBehavior) = 0;

    /// \brief 获得辅助对象是否可以沿着工作平面移动
    virtual bool IsMoveAlongWorkplane() const = 0;

    /// \brief 设置辅助对象是否可以沿着工作平面移动
    virtual void SetMoveAlongWorkplane(bool bMoveAlongWorkplane) = 0;

    /// \brief 判断辅助对象移动的时候，其Master是否可以一起移动
    virtual bool CanMasterElementToMove() const = 0;

    /// \brief 获取Implementation Uid
    static UniIdentity GetImplementationUid();
};
```

### 2. IElementShapeHandleBehavior 夹点行为

定义夹点的具体行为，基于 GCMP SDK 的 IElementShapeHandleBehavior 接口：

```cpp
/// \brief 辅助对象的行为组件，用于扩展辅助对象的行为
class IElementShapeHandleBehavior : public IDbObject
{
public:
    /// \brief 获取所属文档
    virtual IDocument* GetDocument() const = 0;

    /// \brief 获得对应辅助对象的ElementId
    virtual ElementId GetElementShapeHandleId() const = 0;

    /// \brief 设置对应辅助对象的ElementId
    virtual void SetElementShapeHandleId(const ElementId& id) = 0;

    /// \brief 获取辅助对象所属ModelView的ElementId
    virtual ElementId GetModelViewId() const = 0;

    /// \brief 根据移动上下文移动辅助对象
    /// \param moveContext 移动对象的上下文
    /// \return 移动成功返回true，移动失败返回false
    virtual bool Translate(const IElementMoveContext& moveContext) = 0;

    /// \brief 更新辅助对象的位置
    virtual void UpdatePosition() = 0;

    /// \brief 更新辅助对象的位置索引
    virtual void UpdatePositionIndexInMasterElement() = 0;

    /// \brief 获取辅助对象的位置
    /// \param nearPoint 辅助对象的位置
    virtual void GetNearestPoint(Vector3d& nearPoint) const = 0;

    /// \brief 创建辅助对象在指定ModelView中的图形表达
    /// \param modelViewDataAccessor ModelView数据访问器
    /// \return 辅助对象的图形表达
    virtual OwnerPtr<IGraphicsElementShape> CreateGrahicsElementShapeInModelView(
        const IModelViewDataAccessor& modelViewDataAccessor) const;

    /// \brief 创建辅助对象在指定ModelView中的图形表达（兼容接口）
    /// \param pModelView 显示辅助对象的ModelView
    /// \return 辅助对象的图形表达
    virtual OwnerPtr<IGraphicsElementShape> CreateGrahicsElementShapeInModelView(
        const IModelView* pModelView) const;

    /// \brief 完成编辑
    virtual bool FinishEditing() = 0;

    /// \brief 获取辅助对象对应的命令字符串和命令参数
    /// \param commandEnvironment 辅助对象对应的命令环境信息
    /// \param commandString 辅助对象对应的命令字符串
    /// \param commandParamters 对应命令的命令参数
    /// \return 获取成功返回true，失败返回false
    virtual bool GetHandleCommandString(const IObject* commandEnvironment,
                                       std::wstring& commandString,
                                       CommandParameters& commandParamters) const = 0;

    /// \brief 获取辅助对象的计算器
    /// \param calculators 计算器收集器
    virtual void GetCalculators(ICalculatorCollection* calculators) const = 0;

    /// \brief 报告辅助对象的依赖关系
    /// \param reporter 依赖关系报告类
    virtual void ReportParents(IElementParentReporter& reporter) const = 0;
};
```

### 3. IGripPointsShapeHandleBehavior 夹点集合行为

专门用于管理多个夹点的行为，基于 GCMP SDK 的 IGripPointsShapeHandleBehavior 接口：

```cpp
/// \brief 夹点集合行为接口
class IGripPointsShapeHandleBehavior : public IElementShapeHandleBehavior
{
public:
    /// \brief 生成GripPointBehavior作为默认的GripPointsShapeHandleBehavior
    /// \param pDocument 所属文档
    /// \param modelViewId 显示夹点的ModelView的ElementId(切换视图夹点会刷新)
    /// \return GripPointBehavior
    static OwnerPtr<IGripPointsShapeHandleBehavior> CreateGripPointsHandleBehavior(
        IDocument* pDocument,
        const ElementId& modelViewId);

    /// \brief 生成InstanceMovableGripPointBehavior作为默认的GripPointsShapeHandleBehavior
    /// \param pDocument 所属文档
    /// \param modelViewId 显示夹点的ModelView的ElementId(切换视图夹点会刷新)
    /// \return InstanceMovableGripPointBehavior
    static OwnerPtr<IGripPointsShapeHandleBehavior> CreateInstanceMovableGripPointsHandleBehavior(
        IDocument* pDocument,
        const ElementId& modelViewId);

    /// \brief 获取在拖动夹点时优先获取的捕捉类型（一般优先捕捉相同类型）
    /// \return 在拖动夹点时优先获取的捕捉类型
    virtual const std::vector<Int64>& GetSnapPriorityTypes() const = 0;
};
```

## 夹点系统架构图

```mermaid
classDiagram
    class IElementShapeHandle {
        +Create() IElementShapeHandle*
        +GetMasterId() ElementId
        +GetElementShapeHandleBehavior() IElementShapeHandleBehavior*
        +CanMasterElementToMove() bool
        +IsMoveAlongWorkplane() bool
    }
    
    class IElementShapeHandleBehavior {
        <<interface>>
        +GetDocument() IDocument*
        +GetElementShapeHandleId() ElementId
        +GetModelViewId() ElementId
        +Translate(moveContext) bool
        +UpdatePosition() void
        +GetNearestPoint(nearPoint) void
        +CreateGrahicsElementShapeInModelView(modelView) OwnerPtr~IGraphicsElementShape~
        +GetHandleCommandString(env, cmdStr, params) bool
        +FinishEditing() bool
    }
    
    class IGripPointsShapeHandleBehavior {
        +CreateGripPointsHandleBehavior() OwnerPtr~IGripPointsShapeHandleBehavior~
        +CreateInstanceMovableGripPointsHandleBehavior() OwnerPtr~IGripPointsShapeHandleBehavior~
        +GetSnapPriorityTypes() vector~Int64~
    }
    
    class SingleGridGripPointBehavior {
        +Create() OwnerPtr~IElementShapeHandleBehavior~
        +Translate() bool
        +CreateGrahicsElementShapeInModelView() OwnerPtr~IGraphicsElementShape~
    }
    
    class SectionViewSymbolNewGripPointBehavior {
        +Create() OwnerPtr~IElementShapeHandleBehavior~
        +Translate() bool
        +UpdatePosition() void
    }
    
    class StructureSlopeLineGripPointBehavior {
        +Translate() bool
        +CreateGrahicsElementShapeInModelView() OwnerPtr~IGraphicsElementShape~
    }
    
    IElementShapeHandle --> IElementShapeHandleBehavior : uses
    IElementShapeHandleBehavior <|-- IGripPointsShapeHandleBehavior
    IElementShapeHandleBehavior <|-- SingleGridGripPointBehavior
    IElementShapeHandleBehavior <|-- SectionViewSymbolNewGripPointBehavior
    IElementShapeHandleBehavior <|-- StructureSlopeLineGripPointBehavior
```

## 夹点创建流程

### 1. 夹点创建入口

```cpp
// 在 ActionModify 中检测到辅助对象时
if (m_auxElementId.IsValid())
{
    IElement* pElement = pDoc->GetElement(m_auxElementId);
    if (IElementShapeHandle* pElementShapeHandle = quick_cast<IElementShapeHandle>(pElement))
    {
        // 获取夹点行为
        const IElementShapeHandleBehavior* pBehavior = 
            pElementShapeHandle->GetElementShapeHandleBehavior();
        
        // 检查是否为夹点集合行为
        const IGripPointsShapeHandleBehavior* pGripPointsShapeHandleBehavior = 
            quick_cast<IGripPointsShapeHandleBehavior>(pBehavior);
        
        if (pGripPointsShapeHandleBehavior)
        {
            // 获取捕捉优先级类型
            std::vector<Int64> priorityTypes = pGripPointsShapeHandleBehavior->GetSnapPriorityTypes();
            // 设置捕捉预处理器
            SetSnapCandidatesPreprocessor(
                NEW_AS_OWNER_PTR(GbmpSnapEndPointPriorityTypesPreprocessor, priorityTypes));
        }
    }
}
```

### 2. 具体夹点创建示例

以轴网夹点为例：

```cpp
ElementId CreateGripPoint(GridControlPointType type, 
                         const ElementId& modelviewId, 
                         const IElement* pElement)
{
    const IGenericElement* pGenericElement = quick_cast<IGenericElement>(pElement);
    if (!pGenericElement)
        return ElementId::InvalidID;
    
    // 获取控制点位置
    const IElementPositionPoints* pOwnerPositionPoints = pGenericElement->GetPositionPoints();
    Vector3d pos = pOwnerPositionPoints ? 
        pOwnerPositionPoints->GetControlPoint((int)type) : Vector3d::Zero;
    
    // 创建夹点行为
    OwnerPtr<IElementShapeHandleBehavior> pGripPointBehavior = 
        SingleGridGripPointBehavior::Create(pElement->GetDocument(), modelviewId);
    
    if (!pGripPointBehavior)
        return ElementId::InvalidID;
    
    // 创建夹点对象
    IElementShapeHandle* pGripPoint = IElementShapeHandle::Create(
        TransferOwnership(pGripPointBehavior),
        pElement->GetElementId(),
        pos,
        (int)type
    );
    
    return pGripPoint ? pGripPoint->GetElementId() : ElementId::InvalidID;
}
```

## 夹点显示机制

### 1. 图形表达创建

每个夹点都需要实现图形表达：

```cpp
OwnerPtr<IGraphicsElementShape> CreateGrahicsElementShapeInModelView(
    const IModelView* pModelView) const override
{
    // 检查视图类型
    if (PlanViewType != pModelView->GetUserDataId())
        return nullptr;
    
    // 创建图形根节点
    OwnerPtr<IGraphicsElementShape> opGGrepNodeRoot = 
        IGraphicsElementShape::Create(GraphicsRenderLayer::TransientObject);
    
    // 获取夹点位置
    Vector3d position = GetGripPointPosition();
    
    // 创建图形点
    OwnerPtr<IGraphicsPoint> opPoint = IGraphicsPoint::Create(position);
    opPoint->SetDisplayShape(PointDisplayShapeType::Circle);  // 设置为圆形显示
    opGGrepNodeRoot->AddChild(TransferOwnership(opPoint));
    
    // 设置图元ID
    IElementShapeHandle* pElementShapeHandle = GetElementShapeHandle();
    if (pElementShapeHandle)
    {
        opGGrepNodeRoot->SetElementId(pElementShapeHandle->GetElementId());
        opGGrepNodeRoot->SetIsClippable(false);  // 不可裁剪
    }
    
    // 设置样式
    IGraphicsStyleManager* pStyleManager = GetDocument()->GetGraphicsStyleManager();
    if (pStyleManager)
    {
        ElementId gstyleId = pStyleManager->GetGraphicsStyleIdByCategoryUid(
            BuiltInCategoryUniIdentities::BICU_AUXILIARY_ELEMENT);
        opGGrepNodeRoot->SetGraphicsStyleId(gstyleId);
    }
    
    return opGGrepNodeRoot;
}
```

### 2. 夹点样式管理

```cpp
// 夹点使用辅助对象样式
ElementId styleId = pStyleManager->GetGraphicsStyleIdByCategoryUid(
    BuiltInCategoryUniIdentities::BICU_AUXILIARY_ELEMENT);

// 设置夹点属性
opGGrepNodeRoot->SetIsSnappable(false);    // 不可捕捉
opGGrepNodeRoot->SetIsSelectable(true);    // 可选择
opGGrepNodeRoot->SetIsClippable(false);    // 不可裁剪
```

## 夹点操作处理

### 1. 夹点拖拽检测

```cpp
void GbmpActionModify::CollectMoveElements(IDocument* pDoc)
{
    m_moveElementIds.clear();
    
    // 检查是否有辅助对象（夹点）
    if (m_auxElementId.IsValid())
    {
        m_moveElementIds.push_back(m_auxElementId);
        return;
    }
    
    // 否则收集选择集中的图元
    const GraphicsNodeReferenceOwnerPtrSet& selections = 
        ISelection::Get()->GetGraphicsNodeReferences();
    
    for (const auto& selection : selections)
    {
        ElementId elementId = selection->GetElementId();
        const IElement* pElement = pDoc->GetElement(elementId);
        if (pElement && IsElementMovable(pElement))
        {
            m_moveElementIds.push_back(elementId);
        }
    }
}
```

### 2. 夹点移动处理

```cpp
bool GbmpActionModify::OnLButtonUp(IUiView* pCurrentView, const Vector3d& pos)
{
    // 检查是否为夹点移动
    if (m_moveElementIds.size() == 1)
    {
        IElement* pElement = pDoc->GetElement(m_moveElementIds[0]);
        if (IElementShapeHandle* pShapeHandle = quick_cast<IElementShapeHandle>(pElement))
        {
            // 检查主图元是否可移动
            if (!pShapeHandle->CanMasterElementToMove())
            {
                FYI(L"辅助对象的Master对象没有编辑权限，不能通过辅助对象移动Master对象");
                return false;
            }
            
            // 计算移动向量
            Vector3d moveDir = m_endPt - m_startPt;
            
            // 执行夹点修改
            if (m_opModifyElementsBehavior)
            {
                m_opModifyElementsBehavior->ModifyGripPoint(
                    pCurrentView, 
                    m_moveElementIds[0], 
                    m_startPt, 
                    moveDir
                );
            }
        }
    }
}
```

### 3. 夹点行为的移动实现

```cpp
bool SingleGridGripPointBehavior::Translate(const IElementMoveContext& moveContext)
{
    // 获取主图元
    IElement* pOwnerElement = GetMasterElement();
    if (!pOwnerElement)
        return false;
    
    // 获取位置点组件
    IGenericElement* pGenericElement = quick_cast<IGenericElement>(pOwnerElement);
    if (!pGenericElement)
        return false;
    
    IElementPositionPoints* pOwnerElementPosPoint = pGenericElement->GetPositionPoints();
    if (!pOwnerElementPosPoint)
        return false;
    
    // 更新控制点位置
    int positionIndex = static_cast<int>(GetPositionType());
    Vector3d pos = pOwnerElementPosPoint->GetControlPoint(positionIndex);
    pOwnerElementPosPoint->SetControlPoint(positionIndex, pos + moveContext.GetMoveVector());
    
    // 触发关联更新
    std::vector<ElementId> vecGripPointIds = AuxiliaryElementUtils::GetAuxiliaryElements(pOwnerElement);
    return pOwnerElement->GetDocument()->GetRegenerator()->ForceRegeneratingElements(vecGripPointIds);
}
```

## 夹点与捕捉系统集成

### 1. 捕捉优先级设置

```cpp
// 在夹点拖拽时设置捕捉优先级
if (m_auxElementId.IsValid())
{
    IElement* pElement = pDoc->GetElement(m_auxElementId);
    std::vector<Int64> priorityTypes;
    
    if (IElementShapeHandle* pElementShapeHandle = quick_cast<IElementShapeHandle>(pElement))
    {
        const IElementShapeHandleBehavior* pBehavior = 
            pElementShapeHandle->GetElementShapeHandleBehavior();
        const IGripPointsShapeHandleBehavior* pGripPointsShapeHandleBehavior = 
            quick_cast<IGripPointsShapeHandleBehavior>(pBehavior);
            
        if (pGripPointsShapeHandleBehavior)
            priorityTypes = pGripPointsShapeHandleBehavior->GetSnapPriorityTypes();
    }
    
    if (priorityTypes.size() > 0)
    {
        SetSnapCandidatesPreprocessor(
            NEW_AS_OWNER_PTR(GbmpSnapEndPointPriorityTypesPreprocessor, priorityTypes));
        m_oSnapContext->SetSnapCandidatesPreprocessor(GetSnapCandidatesPreprocessor());
    }
}
```

### 2. 夹点最近点计算

```cpp
void GbmpActionModify::GetNearestPoint(IUiView* pCurrentView, 
                                      const Vector3d& inputPoint, 
                                      Vector3d& nearPoint)
{
    // 如果是辅助对象（夹点），直接获取其最近点
    if (m_auxElementId.IsValid())
    {
        IElement* pElement = pCurrentView->GetUiDocument()->GetDbDocument()->GetElement(m_auxElementId);
        IElementShapeHandle* pShapeHandle = quick_cast<IElementShapeHandle>(pElement);
        
        if (pShapeHandle)
        {
            pShapeHandle->GetElementShapeHandleBehavior()->GetNearestPoint(nearPoint);
            return;
        }
    }
    
    // 否则计算普通图元的最近点
    nearPoint = inputPoint;
    // ... 其他计算逻辑
}
```

## 特殊夹点处理

### 1. 表格夹点

```cpp
// 表格夹点的特殊处理
if (pBasicInformation->GetClassficationUid() == 
    GuidUtils::FromString(L"{EDBC1DCC-7BC6-4EE1-BD90-0880B2E3E5C8}"))
{
    // 记录表格项索引
    RecordItemIndex(pDoc);
    
    // 执行夹点修改
    m_opModifyElementsBehavior->ModifyGripPoint(
        pCurrentView, 
        moveElementId, 
        m_startPt, 
        m_endPt - (IsCaught() ? m_nearStartPt : m_startPt)
    );
    
    // 更新其他页面索引
    if (!g_otherPageIndexs.empty()) 
    {
        UpdateSelcetionOrHeight(pDoc);
    }
}
```

### 2. 裁剪框夹点

```cpp
// 裁剪框夹点的坐标转换
const IViewClipRange* pViewClipRange = 
    quick_cast<IViewClipRange>(pDoc->GetElement(pShapeHandle->GetMasterId()));
    
if (pViewClipRange && pViewClipRange->GetSplitType() != EnSplitType::Unknown)
{
    // 需要进行子视图坐标映射
    posOnWP = pCurrentView->GetCanvas()->MapFromSubView(posOnWP);
}
```

## 夹点生命周期管理

### 1. 夹点创建时机

```cpp
// 图元选中时自动创建夹点
void OnSelectionChanged()
{
    // 清除旧夹点
    ClearExistingGripPoints();
    
    // 为选中图元创建夹点
    const GraphicsNodeReferenceOwnerPtrSet& selections = 
        ISelection::Get()->GetGraphicsNodeReferences();
    
    for (const auto& selection : selections)
    {
        CreateGripPointsForElement(selection->GetElementId());
    }
}
```

### 2. 夹点清理

夹点清理通过 `m_auxElementId` 管理，在 GbmpActionModify 中的实际调用示例：

```cpp
// 1. Reset() 方法中清理夹点数据
void GbmpActionModify::Reset()
{
    m_startPt = m_endPt = m_interPt = m_nearStartPt = Vector3d(0, 0, 0);
    m_isCaught = false;
    m_status = PS_GBMP_NOTSTART;

    // 清理移动图元列表（包含夹点ID）
    m_moveElementIds.clear();

    // 清理影子对象
    ClearShadows();

    // 清理捕捉渲染数据
    ISnapRender::Get()->ClearSnapRenderData();
}

// 2. 操作完成后清理
void GbmpActionModify::OnLButtonUp(IUiView* pCurrentView, const Vector3d& pos)
{
    // 移动操作完成后清理
    bool cleared = ClearShadows();
    if (cleared)
    {
        // 执行实际移动操作
        // ...
    }

    // 重置状态，包括清理夹点
    Reset();
    UpdateView();
}

// 3. 选择集变化时清理
void CollectMoveElements(IDocument* pDoc)
{
    m_moveElementIds.clear();  // 清理包括夹点在内的移动图元

    if (m_auxElementId.IsValid())  // 如果有夹点被选中
    {
        m_moveElementIds.push_back(m_auxElementId);
    }
    else
    {
        // 收集选择集中的图元
        // ...
    }
}
```

**ClearGripPoints 被调用的典型场景**：

1. **操作取消时**: `ActionCancelled()` → `Reset()` → 清理夹点数据
2. **操作完成时**: `OnLButtonUp()` → `Reset()` → 清理夹点数据
3. **选择集变化时**: `OnSelectionChanged()` → `CollectMoveElements()` → 清理旧夹点
4. **视图切换时**: `OnViewSwitched()` → 清理当前视图夹点
5. **拖拽开始时**: `OnLButtonDown()` → 根据情况清理或重新收集夹点

## 调试支持

### 1. 夹点调试模式

```cpp
CREATE_DEBUG_MODE(CtlrDontNeedTableItemGripPoints, 
    L"Ctrl选择不需要生成单元格夹点", 
    gcmp::DebugModeGroup::DMGT_GREP_AND_PICKSNAP, 
    L"GDMPLab", L"2023-12-20");

CREATE_DEBUG_MODE(ShiftUpdateSelectionSelectionForTable, 
    L"Shift按下处理表格选择集更新", 
    gcmp::DebugModeGroup::DMGT_GREP_AND_PICKSNAP, 
    L"GDMPLab", L"2023-12-20");
```

### 2. 夹点信息输出

```cpp
// 在状态栏显示夹点信息
std::wstring GetPromptMessage() const
{
    if (m_auxElementId.IsValid())
    {
        IElement* pElement = GetDoc()->GetElement(m_auxElementId);
        if (IElementShapeHandle* pShapeHandle = quick_cast<IElementShapeHandle>(pElement))
        {
            return L"夹点: " + pShapeHandle->GetBasicInformation()->GetName();
        }
    }
    return ActionBase::GetPromptMessage();
}
```
