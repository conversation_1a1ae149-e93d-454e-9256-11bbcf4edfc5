## 墙的父子关系管理机制

墙图元在GDMP中实现了完整的父子关系管理机制，通过`ReportParents`和`UpdateForWeakParentDeletion`方法来管理与其他图元的依赖关系。

### Strong Parent vs Weak Parent概念

GDMP中的图元依赖关系分为两种类型：

1. **Strong Parent（强父图元）**：当强父图元被删除时，当前图元也会被系统自动删除
2. **Weak Parent（弱父图元）**：当弱父图元被删除时，当前图元会收到通知，可以进行相应的处理而不被删除

### 墙图元的父子关系实现

#### StructureWall基类实现

`StructureWall`作为墙图元的基础数据类，提供了父子关系管理的基本实现：

```cpp
void StructureWall::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
{
    return;
}

void StructureWall::ReportParents(IElementParentReporter& reporter) const
{
}
```

基类`StructureWall`的实现为空，表明墙图元本身不直接依赖其他图元。

#### StructureBasicWall实现

基本墙通过委托模式将父子关系管理委托给基础数据：

```cpp
void StructureBasicWall::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
{
    GetBaseDataFW__()->UpdateForWeakParentDeletion(deletedElementIds);
}

void StructureBasicWall::ReportParents(IElementParentReporter& reporter) const
{
    GetBaseData__()->ReportParents(reporter);
}
```

#### StructureVariableSectionWall实现

变截面墙同样采用委托模式：

```cpp
void StructureVariableSectionWall::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
{
    GetBaseDataFW__()->UpdateForWeakParentDeletion(deletedElementIds);
}

void StructureVariableSectionWall::ReportParents(IElementParentReporter& reporter) const
{
    GetBaseData__()->ReportParents(reporter);
}
```

### 父子关系管理的调用流程

1. **依赖关系报告**：系统调用`ReportParents`方法收集图元的依赖关系
2. **弱父图元删除处理**：当弱父图元被删除时，系统调用`UpdateForWeakParentDeletion`方法通知相关图元
3. **委托处理**：墙的具体实现类将处理委托给基础数据类`StructureWall`

### 设计特点

墙图元的父子关系管理具有以下特点：

1. **简洁性**：墙图元本身不直接依赖其他图元，因此实现较为简洁
2. **委托模式**：具体实现类通过委托模式统一处理，保持代码一致性
3. **扩展性**：如果将来需要添加依赖关系，可以在基类中统一实现

## 墙计算器注册

墙计算器通过ReportInputDataIds方法声明其依赖的输入数据，当这些数据发生变化时，系统会自动触发计算器的Execute方法执行计算。

### 基本墙计算器注册

StructureBasicWall通过GetCalculators方法注册计算器，但实际上它将计算器的注册委托给了BaseData（StructureWall对象）：

```cpp
void StructureBasicWall::GetCalculators(ICalculatorCollection * calculators) const
{
    GetBaseData__()->GetCalculators(calculators);
}
```

### 变截面墙计算器注册

StructureVariableSectionWall同样将计算器注册委托给了BaseData（StructureWall对象）：

```cpp
void StructureVariableSectionWall::GetCalculators(ICalculatorCollection * calculators) const
{
    GetBaseData__()->GetCalculators(calculators);
}
```

### 墙通用计算器注册

StructureWall的GetCalculators方法注册了以下计算器：

```cpp
void StructureWall::GetCalculators(ICalculatorCollection * calculators) const
{
    ADD_CALCULATOR(InstanceVolumeCalculator, GetDocument(), GetVolumeRdId());
    ADD_CALCULATOR(StructureInstanceLengthCalculator, GetDocument(), GetLengthRdId());
    ADD_CALCULATOR(WallAreaCalculator, GetDocument(), GetAreaRdId());
    ADD_CALCULATOR(WallShapeChangedCalculator, GetDocument(), GetWallShapeChangedRdId());
}
```

这些计算器分别负责计算墙的体积、长度、面积，以及处理墙形状变化的关联更新。

## 墙的计算器

### InstanceVolumeCalculator

InstanceVolumeCalculator负责计算墙的体积：

```cpp
class InstanceVolumeCalculator : public GbmpCalculatorBase
{
    DECLARE_CALCULATOR(InstanceVolumeCalculator, IInstance)
public:
    InstanceVolumeCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : GbmpCalculatorBase(pDoc, outputDataId) {}
    virtual void ReportInputDataIds(std::vector<RegenDataId> & dataIds) const override;
    virtual void Execute() override;
};
```

该计算器继承自GbmpCalculatorBase，定义了两个关键方法：ReportInputDataIds和Execute。

```cpp
void InstanceVolumeCalculator::ReportInputDataIds(std::vector<RegenDataId> & dataIds) const
{
    IInstance* pTarget = GetElement<IInstance>();
    const IElementModelShape* pGrepBehavior = pTarget->GetElementModelShape();
    dataIds.push_back(pGrepBehavior->GetGraphicsElementShapeRdId());
}
```

ReportInputDataIds方法报告了该计算器依赖的输入数据ID，这里依赖的是墙的图形表达（GraphicsElementShape）的RegenDataId。当墙的图形表达发生变化时，会触发该计算器执行。

```cpp
void InstanceVolumeCalculator::Execute()
{
    IInstance* pInstance = GetElement<IInstance>();

    double volume = StructureInstanceUtils::GetInstanceVolume(pInstance);
    UniIdentity paramUid = PARAMETER_UID(VolumeBuiltInParameter);
    StructureInstanceUtils::SetInstanceOutputParamValueByParamUid(pInstance, volume, paramUid);
}
```

Execute方法执行实际的计算，它调用StructureInstanceUtils::GetInstanceVolume函数计算墙的体积，然后将结果设置到墙的VolumeBuiltInParameter参数中。

GetInstanceVolume函数通过分析墙的几何形状来计算墙的体积。当墙的尺寸、形状或其他属性发生变化时，会触发该计算器重新计算墙的体积。

### StructureInstanceLengthCalculator

StructureInstanceLengthCalculator负责计算墙的长度：

```cpp
class StructureInstanceLengthCalculator : public GbmpCalculatorBase
{
    DECLARE_CALCULATOR(StructureInstanceLengthCalculator, IInstance)
public:
    StructureInstanceLengthCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : GbmpCalculatorBase(pDoc, outputDataId) {}
    virtual void ReportInputDataIds(std::vector<RegenDataId> & dataIds) const override;
    virtual void Execute() override;
};
```

该计算器也继承自GbmpCalculatorBase，定义了ReportInputDataIds和Execute方法。

```cpp
void StructureInstanceLengthCalculator::ReportInputDataIds(std::vector<RegenDataId> & dataIds) const
{
    IInstance* pTarget = GetElement<IInstance>();

    const IElementModelShape* pGRepBehavior = pTarget->GetElementModelShape();

    dataIds.push_back(pGRepBehavior->GetGraphicsElementShapeRdId());
}
```

与InstanceVolumeCalculator类似，ReportInputDataIds方法也报告了对墙的图形表达的依赖。

```cpp
void StructureInstanceLengthCalculator::Execute()
{
    IInstance* pInstance = GetElement<IInstance>();

    double length(0);
    if (StructureInstanceUtils::GetInstanceLength(pInstance, length))
    {
        UniIdentity paramUid = PARAMETER_UID(LineFamilyLengthBuiltInParameter);
        StructureInstanceUtils::SetInstanceOutputParamValueByParamUid(pInstance, length, paramUid);
    }
}
```

Execute方法调用StructureInstanceUtils::GetInstanceLength函数计算墙的长度，然后将结果设置到墙的LineFamilyLengthBuiltInParameter参数中。

GetInstanceLength函数的实现如下：

```cpp
bool StructureInstanceUtils::GetInstanceLength(const IInstance* pInstance, double& length)
{
    std::vector<Vector3d> controlPts = GetInstanceControlPoints(pInstance);
    if (controlPts.size() == 2)
    {
        length = Vector3d(controlPts[1] - controlPts[0]).Length();
        return true;
    }
    else
    {
        // 圆弧暂不支持倾斜
        bool bOk = GetInstanceHorizontalLength(pInstance, length);
        return true;
    }
}
```

对于直线墙，长度计算为起点和终点之间的距离；对于弧形墙，长度计算为弧的水平投影长度。

### WallAreaCalculator

WallAreaCalculator负责计算墙的面积：

```cpp
class WallAreaCalculator : public gcmp::GbmpCalculatorBase
{
    DECLARE_CALCULATOR(WallAreaCalculator, IInstance)
public:
    WallAreaCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : GbmpCalculatorBase(pDoc, outputDataId) {}
    virtual void ReportInputDataIds(std::vector<RegenDataId> & dataIds) const override;
    virtual void Execute() override;
};
```

```cpp
void WallAreaCalculator::ReportInputDataIds(std::vector<RegenDataId> & dataIds) const
{
    IInstance *pTarget = GetElement<IInstance>();
    const IElementModelShape *pGrepBehavior = pTarget->GetElementModelShape();
    dataIds.push_back(pGrepBehavior->GetGraphicsElementShapeRdId());
}
```

ReportInputDataIds方法报告了该计算器依赖的输入数据ID，这里依赖的是墙的图形表达（GraphicsElementShape）的RegenDataId。当墙的图形表达发生变化时，会触发该计算器执行。

```cpp
void WallAreaCalculator::Execute()
{
    IInstance *pInstance = GetElement<IInstance>();

    // 计算面积
    double area = GetWallArea(pInstance);
    UniIdentity paramUId = PARAMETER_UID(AreaBuiltInParameter);
    StructureInstanceUtils::SetInstanceOutputParamValueByParamUid(pInstance, area, paramUId);
}
```

Execute方法执行实际的计算，它调用GetWallArea函数计算墙的面积，然后将结果设置到墙的AreaBuiltInParameter参数中。

WallAreaCalculator的面积计算逻辑比较复杂，主要步骤如下：

1. 获取墙的外表面和内表面的面ID
2. 检查墙是否有被切割或开洞
3. 如果有切割或开洞，则通过GraphicsNodeIdHistoryUtils追溯当前的面ID
4. 计算外表面和内表面的面积
5. 取两者中的较大值作为墙的面积

### WallShapeChangedCalculator

WallShapeChangedCalculator负责处理墙形状变化的关联更新：

```cpp
class WallShapeChangedCalculator : public GbmpCalculatorBase
{
    DECLARE_CALCULATOR(WallShapeChangedCalculator, IInstance)
public:
    WallShapeChangedCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : GbmpCalculatorBase(pDoc, outputDataId) {}
    virtual void ReportInputDataIds(std::vector<RegenDataId> & oInputDatas) const override;
    virtual void Execute() override;
};
```

```cpp
void WallShapeChangedCalculator::ReportInputDataIds(std::vector<RegenDataId> & oInputDatas) const
{
    const IInstance* pElement = GetElement<IInstance>();

    RegenDataId baseGRepRdId = pElement->GetBaseGraphicsElementShapeComponent()->GetBaseGraphicsElementShapeRdId();
    oInputDatas.push_back(baseGRepRdId);
}
```

该计算器依赖墙的基础图形表达的RegenDataId。当墙的基础图形表达发生变化时，会触发该计算器执行。

```cpp
void WallShapeChangedCalculator::Execute()
{
    IElement* pElement = GetTarget();

    IInstance* pWall = quick_cast<IInstance>(pElement);

    //直墙
    if (IExternalDataComponent * pExternalDataComponent = pWall->GetExternalDataComponent())
    {
        auto pData0 = pExternalDataComponent->FindExternalData(StructureBasicWall::GetStaticClassSchema()->GetName());
        if (pData0 != nullptr)
        {
            StructureBasicWall* pWall = dynamic_cast<StructureBasicWall*>(pData0);
            pWall->GetBaseDataFW()->SetWallShapeChanged(true);
        }

        //变截面墙
        auto pData1 = pExternalDataComponent->FindExternalData(StructureVariableSectionWall::GetStaticClassSchema()->GetName());
        if (pData1 != nullptr)
        {
            StructureVariableSectionWall* pWall = dynamic_cast<StructureVariableSectionWall*>(pData1);
            pWall->GetBaseDataFW()->SetWallShapeChanged(true);
        }
    }
}
```

Execute方法将墙的WallShapeChanged属性设置为true，这会触发墙形状变化的关联更新。该计算器同时处理基本墙和变截面墙，通过查找对应的ExternalData来设置相应的属性。

当墙的形状发生变化时，WallShapeChanged标志会被设置为true，这将触发墙的关联更新，例如重新计算墙的几何参数、更新墙的图形表达等。

