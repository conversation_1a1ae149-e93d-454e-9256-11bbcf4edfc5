# ActionModify 代码分析计划

## 概述

本文档制定了对 GDMPLab 中 ActionModify 相关代码的分析计划。ActionModify 是 GDMP 平台中负责图元修改操作的核心交互组件，包含了拾取、选择、拖拽、夹点操作、上下文菜单等多个维度的功能。

## 核心文件

### 基础类
- `GbmpPickActionBase.h/cpp` - 拾取操作基类
- `GbmpActionModify.h/cpp` - 修改操作主类

### 行为定制类
- `GbmpModifyElementsBehavior.h/cpp` - 修改行为实现
- `ActionModifyDefaultBehavior.h` (GDMP SDK) - 默认修改行为

### 相关接口
- `IActionModifyBehavior.h` (GDMP SDK) - 修改行为接口
- `IElementShapeHandle.h` (GDMP SDK) - 夹点处理接口

## 分析维度

### 1. 架构设计维度
- **继承关系**: GbmpActionModify → GbmpPickActionBase → ActionBase
- **组合关系**: ActionModify 与 Behavior、EventHandler、PickFilter 等组件的组合
- **接口设计**: GDMP SDK 提供的接口与 GDMPLab 实现的关系

### 2. 功能模块维度
- **拾取机制**: 点选、框选、过滤器
- **选择集管理**: 选择集变化、多选逻辑
- **拖拽操作**: 影子对象、移动向量计算
- **夹点系统**: 夹点生成、夹点拖拽
- **捕捉系统**: 近程捕捉、远程捕捉
- **上下文菜单**: 右键菜单定制

### 3. 交互流程维度
- **鼠标事件流**: 按下、移动、抬起、双击
- **键盘事件流**: 按键处理、组合键
- **状态管理**: 操作状态转换
- **事件传递**: 事件处理链

### 4. 扩展机制维度
- **Behavior 模式**: 行为定制机制
- **EventHandler 模式**: 事件处理器扩展
- **Filter 模式**: 拾取过滤器扩展

## 文档结构规划

### 主要文档
1. **01_架构设计.md** - 整体架构和设计模式
2. **02_拾取机制.md** - 拾取相关的核心逻辑
3. **03_选择集管理.md** - 选择集的管理和变化处理
4. **04_拖拽操作.md** - 拖拽移动的实现机制
5. **05_夹点系统.md** - 夹点的生成、显示和操作
6. **06_捕捉系统.md** - 近程和远程捕捉机制
7. **07_事件处理流程.md** - 鼠标和键盘事件的处理流程
8. **08_扩展机制.md** - Behavior、EventHandler、Filter 等扩展机制

### 交叉维度文档
9. **09_夹点操作详解.md** - 夹点相关的完整操作流程
10. **10_上下文菜单.md** - 上下文菜单的基础架构、生成流程和高级定制机制

### 辅助组件文档
11. **11_工具类和辅助组件.md** - 移动工具类、过滤器、事务管理等辅助组件

## 分析方法

### 代码阅读顺序
1. 先读接口定义，理解设计意图
2. 再读基类实现，掌握基础功能
3. 最后读具体实现，了解定制逻辑

### 重点关注点
- **设计模式的应用**: Strategy、Observer、Chain of Responsibility 等
- **GDMP SDK 接口的使用**: 如何正确使用 GDMP 提供的接口
- **性能考虑**: 大场景下的性能优化
- **扩展性设计**: 如何支持新的图元类型和操作

### 分析工具
- **类图**: 使用 Mermaid 绘制类关系图
- **时序图**: 绘制关键操作的时序图
- **流程图**: 绘制复杂逻辑的流程图

## 预期成果

通过本次分析，期望达到以下目标：

1. **理解架构**: 深入理解 ActionModify 的整体架构设计
2. **掌握机制**: 掌握拾取、选择、拖拽、夹点等核心机制
3. **学习模式**: 学习 GDMP 平台的设计模式和扩展机制
4. **指导开发**: 为后续的功能开发和扩展提供指导

## 注意事项

1. **版本兼容**: 注意不同版本 GDMP SDK 的接口差异
2. **平台特性**: 关注 Windows 平台特有的实现
3. **性能优化**: 重点关注大场景下的性能优化策略
4. **调试技巧**: 记录调试和问题排查的技巧

## 后续计划

1. 按照文档结构逐步完成各个模块的分析
2. 根据分析结果补充和完善文档结构
3. 整理最佳实践和开发指南
4. 形成完整的 ActionModify 开发文档体系
