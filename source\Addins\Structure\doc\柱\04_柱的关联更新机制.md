## 柱的父子关系管理机制

柱图元在GDMP中实现了父子关系管理机制，通过`ReportParents`和`UpdateForWeakParentDeletion`方法来管理与其他图元的依赖关系。

### Strong Parent vs Weak Parent概念

GDMP中的图元依赖关系分为两种类型：

1. **Strong Parent（强父图元）**：当强父图元被删除时，当前图元也会被系统自动删除
2. **Weak Parent（弱父图元）**：当弱父图元被删除时，当前图元会收到通知，可以进行相应的处理而不被删除

### 柱图元的父子关系实现

#### StructureColumn基类实现

`StructureColumn`作为柱图元的基础数据类，提供了父子关系管理的基本实现：

```cpp
void StructureColumn::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
{
    return;
}

void StructureColumn::ReportParents(IElementParentReporter& reporter) const
{
}
```

基类`StructureColumn`的实现为空，表明柱图元本身不直接依赖其他图元。

#### StructureVerticalColumn实现

垂直柱通过委托模式将父子关系管理委托给基础数据：

```cpp
void gcmp::StructureVerticalColumn::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
{
    return GetBaseDataFW__()->UpdateForWeakParentDeletion(deletedElementIds);
}

void gcmp::StructureVerticalColumn::ReportParents(IElementParentReporter& reporter) const
{
    GetBaseData__()->ReportParents(reporter);
}
```

#### StructureSlantedColumn实现

斜柱同样采用委托模式：

```cpp
void StructureSlantedColumn::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
{
    GetBaseDataFW__()->UpdateForWeakParentDeletion(deletedElementIds);
}

void StructureSlantedColumn::ReportParents(IElementParentReporter& reporter) const
{
    GetBaseData__()->ReportParents(reporter);
}
```

### 父子关系管理的调用流程

1. **依赖关系报告**：系统调用`ReportParents`方法收集图元的依赖关系
2. **弱父图元删除处理**：当弱父图元被删除时，系统调用`UpdateForWeakParentDeletion`方法通知相关图元
3. **委托处理**：柱的具体实现类将处理委托给基础数据类`StructureColumn`

### 设计特点

柱图元的父子关系管理具有以下特点：

1. **简洁性**：柱图元本身不直接依赖其他图元，因此实现较为简洁
2. **委托模式**：具体实现类通过委托模式统一处理，保持代码一致性
3. **扩展性**：如果将来需要添加依赖关系，可以在基类中统一实现

柱在GDMP中是一种重要的结构构件，当柱的属性（如位置、尺寸等）发生变化时，需要自动更新其他相关属性（如体积、角度等），以保持模型的一致性。这种关联更新机制是通过GDMP的计算器系统实现的。

GDMP中的计算器是实现关联更新的核心机制。柱相关的计算器主要包括：

1. **基础数据计算器**：更新柱的基本属性，如体积等
2. **斜柱计算器**：更新斜柱的特有属性，如倾斜角度等

## 基础数据计算器

基础数据计算器是通过`StructureColumn`类的`GetCalculators`方法注册的：

```cpp
void StructureColumn::GetCalculators(ICalculatorCollection * calculators) const
{
    ADD_CALCULATOR(InstanceVolumeCalculator, GetDocument(), GetVolumeRdId());
}
```

### 体积计算器

`InstanceVolumeCalculator`用于计算柱的体积：

```cpp
class InstanceVolumeCalculator : public GbmpCalculatorBase
{
    DECLARE_CALCULATOR(InstanceVolumeCalculator, IInstance)
public:
    InstanceVolumeCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : GbmpCalculatorBase(pDoc, outputDataId) {}

    virtual void ReportInputDataIds(std::vector<RegenDataId> & dataIds) const override
    {
        IInstance* pTarget = GetElement<IInstance>();
        const IElementModelShape* pGrepBehavior = pTarget->GetElementModelShape();
        dataIds.push_back(pGrepBehavior->GetGraphicsElementShapeRdId());
    }

    virtual void Execute() override
    {
        IInstance* pInstance = GetElement<IInstance>();

        double volume = StructureInstanceUtils::GetInstanceVolume(pInstance);
        UniIdentity paramUid = PARAMETER_UID(VolumeBuiltInParameter);
        StructureInstanceUtils::SetInstanceOutputParamValueByParamUid(pInstance, volume, paramUid);
    }
};
```

该计算器依赖柱的图形表达（GraphicsElementShape）的RegenDataId。当柱的图形表达发生变化时，会触发该计算器执行，重新计算柱的体积并更新到参数中。

## 垂直柱计算器

垂直柱（`StructureVerticalColumn`）将计算器注册委托给基础柱：

```cpp
void StructureVerticalColumn::GetCalculators(ICalculatorCollection * calculators) const
{
    GetBaseData__()->GetCalculators(calculators);
}
```

垂直柱没有额外的计算器，只使用基础柱的体积计算器。

## 斜柱计算器

斜柱（`StructureSlantedColumn`）除了基础柱的计算器外，还注册了角度计算器：

```cpp
void StructureSlantedColumn::GetCalculators(ICalculatorCollection * calculators) const
{
    GetBaseData__()->GetCalculators(calculators);
    const IElement *pElement = GetOwnerElement();

    const IElementParameters* pElementParameters = pElement->GetElementParameters();

    ADD_CALCULATOR_OVERRIDE_REGENDATA(SlantedColumnAngleCalculator, GetDocument(), pElementParameters->GetCoordinateSystemParameterRdId());
}
```

### 角度计算器

`SlantedColumnAngleCalculator`用于计算斜柱的倾斜角度：

```cpp
class SlantedColumnAngleCalculator : public GbmpCalculatorBase
{
    DECLARE_CALCULATOR(SlantedColumnAngleCalculator, IInstance)
public:
    SlantedColumnAngleCalculator(IDocument* pDoc, const RegenDataId& outputDataId) : GbmpCalculatorBase(pDoc, outputDataId) {}

    virtual void ReportInputDataIds(std::vector<RegenDataId> & dataIds) const override
    {
        const IElement* pElement = GetElement<IElement>();

        const IElementParameters* pElementParameters = pElement->GetElementParameters();
        dataIds.push_back(pElementParameters->GetDriveParameterRdId());

        const IElementTransformationComponent* pElementPositionPoints = pElement->GetElementTransformationComponent();
        dataIds.push_back(pElementPositionPoints->GetTransformedRdId(pElement->GetElementId()));
    }

    virtual void Execute() override
    {
        IInstance* pElement = GetElement<IInstance>();

        IElementParameters* pElementParameters = pElement->GetElementParameters();

        OwnerPtr<IParameter> opHeightParam = pElementParameters->GetParameterByUid(PARAMETER_UID(ColumnHeightBuiltInParameter));
        double height = opHeightParam->GetValueAsDouble();

        const IElementPosition* posBehavior = pElement->GetElementPosition();
        const IPositionGeometry* pGeometry = posBehavior->GetPositionGeometry();
        const IPositionCurve2d *pPosCurve2d = quick_cast<IPositionCurve2d>(pGeometry);

        //二维定位线
        const ILine2d *pLine2d = quick_cast<ILine2d>(pPosCurve2d->GetBaseCurve());
        Vector2d dir;
        if (pLine2d)
        {
            Vector2d startPt = pLine2d->GetStartPoint();
            Vector2d endPt = pLine2d->GetEndPoint();
            dir = endPt - startPt;
        }

        double angle = std::atan2(dir.Length(), height);
        OwnerPtr<IParameter> opAngleParam = pElementParameters->GetParameterByUid(PARAMETER_UID(ColumnAngleBuiltInParameter));

        opAngleParam->SetValueAsDouble(angle);
        pElement->GetElementParameters()->SetParameter(opAngleParam.get());
    }
};
```

该计算器依赖柱的参数（ElementParameters）和变换组件（ElementTransformationComponent）的RegenDataId。当柱的参数或位置发生变化时，会触发该计算器执行，重新计算斜柱的倾斜角度并更新到参数中。

计算角度的方法是通过柱的二维定位线的起点和终点计算水平方向的长度，然后与柱的高度一起计算倾斜角度（使用反正切函数）。

## 关联更新流程

柱的关联更新流程主要包括以下步骤：

1. **更新触发**：当柱的参数、控制点或关联对象发生变化时，会触发关联更新。例如，修改柱的高度、移动柱的位置点等。

2. **数据依赖收集**：通过计算器的`ReportInputDataIds`方法收集数据依赖。例如，体积计算器依赖图形表达，角度计算器依赖参数和变换组件。

3. **计算器执行**：当依赖的数据发生变化时，GDMP的关联更新系统会调用相应计算器的`Execute`方法。例如，当柱的图形表达变化时，会执行体积计算器；当斜柱的位置或高度变化时，会执行角度计算器。

4. **参数更新**：计算器执行后，会更新相应的参数，如体积、角度等。这些参数可以在属性面板中显示，也可以被其他计算器或组件使用。

5. **图形表达更新**：参数更新后，可能会触发图形表达的更新。例如，角度参数的变化可能会影响柱的图形表达。

这种关联更新机制使得柱的参数能够实时反映其几何特性的变化，提高了模型的一致性和准确性。当用户修改柱的属性时，相关的计算结果会自动更新，无需手动干预。
