## 轴网的父子关系管理机制

轴网图元在GDMP中实现了复杂的父子关系管理机制，通过`ReportParents`和`UpdateForWeakParentDeletion`方法来管理与关联平面、参数绑定、图形引用等多种图元的依赖关系。

### Strong Parent vs Weak Parent概念

GDMP中的图元依赖关系分为两种类型：

1. **Strong Parent（强父图元）**：当强父图元被删除时，当前图元也会被系统自动删除
2. **Weak Parent（弱父图元）**：当弱父图元被删除时，当前图元会收到通知，可以进行相应的处理而不被删除

### 轴网图元的父子关系实现

#### SingleGrid实现

轴网图元建立了多种类型的依赖关系：

```cpp
void gcmp::SingleGrid::ReportParents(IElementParentReporter& reporter) const
{
    const IElement* pElement = GetOwnerElement();
    const IElementPosition *posBehavior = pElement->GetElementPosition();
    if (posBehavior != nullptr)
    {
        reporter.ReportStrong(posBehavior->GetBaseAssociatedPlaneId());
    }

    // 报告参数绑定的弱引用
    auto& embeddedParamMap = GetOwnerElement()->GetElementParameters()->GetEmbeddedParametersMap();
    FOR_EACH(iter, embeddedParamMap)
    {
        const IParameterValueElementId *pElementIdParamValue = dynamic_cast<const IParameterValueElementId *>(iter.second->GetParameterValueStorage());
        if (pElementIdParamValue != nullptr)
        {
            reporter.ReportWeak(pElementIdParamValue->GetValue());
        }
    }

    reporter.ReportStrong(GetOwnerElement()->GetBasicInformation()->GetTypeId());

    // 报告图形引用的弱引用
    const IGraphicsElementShape* pGrep = pElement->GetElementModelShape()->GetGraphicsElementShape();
    if (pGrep)
    {
        std::vector<ElementId> ids;
        pGrep->GetReferencedElementIds(ids);
        reporter.ReportWeak(ids);
    }
}
```

```cpp
void gcmp::SingleGrid::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
{
    IElement* pElement = GetOwnerElement();

    // 处理参数绑定的弱引用删除
    IElementParameterBindings* pBehavior = GetOwnerElement()->GetElementParameters()->GetElementParameterBindings();
    if (pBehavior)
    {
        auto& bindingInfos = pBehavior->GetParameterBindings();
        for (int ii = (int)bindingInfos.size(); ii > 0; --ii)
        {
            const IParameterBinding* info = bindingInfos[ii - 1].get();
            IElement* pSourceElement = GetDocument()->GetElement(info->GetSourceElementId());
            if (pSourceElement == nullptr)
            {
                continue;
            }
            // 处理参数绑定源图元的删除...
        }
    }
}
```

### 轴网的多重依赖关系

#### 强引用关系

1. **关联平面依赖**：轴网对其关联平面采用强引用，确保平面删除时轴网也被删除
2. **类型依赖**：轴网对其类型图元采用强引用，保证类型定义的完整性

#### 弱引用关系

1. **参数绑定依赖**：轴网对参数绑定的源图元采用弱引用，源图元删除时清理绑定关系
2. **图形引用依赖**：轴网对图形表达中引用的图元采用弱引用，支持图形引用的动态管理

### 父子关系管理的调用流程

```mermaid
sequenceDiagram
    participant System as GDMP系统
    participant Grid as 轴网
    participant Plane as 关联平面
    participant Binding as 参数绑定
    participant Graphics as 图形引用

    Note over System: 依赖关系收集阶段
    System->>Grid: ReportParents()
    Grid->>System: ReportStrong(关联平面ID)
    Grid->>System: ReportStrong(类型ID)
    Grid->>System: ReportWeak(参数绑定源图元ID)
    Grid->>System: ReportWeak(图形引用图元ID)

    Note over System: 弱父图元删除阶段
    System->>Binding: 删除参数绑定源图元
    System->>Graphics: 删除图形引用图元
    System->>Grid: UpdateForWeakParentDeletion(删除的ID集合)
    Grid->>Grid: 清理无效的参数绑定
    Grid->>Grid: 处理图形引用更新
```

### 设计特点

轴网的父子关系管理具有以下特点：

1. **多重依赖**：轴网建立了强引用和弱引用的多重依赖关系
2. **参数绑定处理**：对参数绑定采用弱引用，支持动态绑定管理
3. **图形引用管理**：对图形表达中的引用采用弱引用，支持图形的灵活组织
4. **类型安全**：对关联平面和类型采用强引用，确保几何和类型的一致性

轴网SingleGrid本身没有实现计算器，轴网相关的计算在只有控制点组件有SingleGridGripPointPositionCalculator，在控制点部分做了介绍。