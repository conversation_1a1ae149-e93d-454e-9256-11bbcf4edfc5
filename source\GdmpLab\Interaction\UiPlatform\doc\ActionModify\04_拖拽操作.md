# ActionModify 拖拽操作

## 概述

拖拽操作是 ActionModify 的核心功能，负责处理图元的移动、夹点拖拽等交互操作。拖拽过程包括影子对象创建、移动向量计算、捕捉处理、实际修改等多个阶段。

## HandledByOtherAction 机制

HandledByOtherAction 是 Action 输出参数中的一个重要标志，用于指示当前操作是否应该由其他 Action 处理：

```cpp
struct ActionOutput
{
    ActionFinishStatus FinishStatus = ActionFinishStatus::Unfinished;
    ActionFinishReason FinishReason = ActionFinishReason::Unknown;
    std::wstring InputString;
    OwnerPtr<IActionOutputData> ActionOutputData;
    bool IsParentActionNeedCancel = true;  // 子Action退出时父Action是否需要退出
};
```

**使用场景**：
- **子 Action 完成后的控制流**: 当子 Action 完成时，通过 `IsParentActionNeedCancel` 标志决定父 Action 是否应该继续执行
- **Action 链式调用**: 在复杂的交互流程中，一个 Action 可能需要启动另一个 Action 来完成特定任务
- **工作平面设置**: 如 `GbmpSetupWorkPlaneAction` 启动 `GbmpSetupWorkPlaneByPickAction` 来拾取面设置工作平面

**典型应用**：
```cpp
void GbmpSetupWorkPlaneAction::OnChildActionFinished(IUiView* pCurrentView, const ActionOutput& childActionReturnParam)
{
    // 根据子Action的返回参数决定后续处理
    if (childActionReturnParam.FinishStatus == ActionFinishStatus::Successful)
    {
        // 子Action成功完成，继续处理
        ProcessChildActionResult(childActionReturnParam);
    }
    else if (childActionReturnParam.IsParentActionNeedCancel)
    {
        // 子Action要求父Action取消
        MarkFinishStatus(ActionFinishStatus::Cancelled);
    }
}
```

## 工作平面约束

### 1. 拖拽是否限制在工作平面

**答案：是的**，拖拽操作通常限制在当前工作平面上进行：

- **工作平面定义**: 每个 ModelView 都有一个关联的工作平面（WorkPlane），定义了当前的绘制和编辑平面
- **拖拽约束**: 图元拖拽时，移动向量会被投影到当前工作平面上，确保操作在二维平面内进行
- **三维视图例外**: 在三维视图中，可能允许更自由的三维拖拽操作

### 2. 工作平面设置机制

```cpp
// 通过参照平面设置工作平面
void SetWorkPlaneByReferencePlane(IModelView* pCurrentView, const IReferencePlane* refPlane)
{
    if (refPlane && pCurrentView)
    {
        OwnerPtr<IPlane> gpln = IPlane::Create(
            refPlane->GetOrigin(),
            refPlane->GetXAxis(),
            refPlane->GetYAxis()
        );
        pCurrentView->SetWorkPlane(gpln.get());
    }
}

// 通过坐标系设置工作平面
void SetWorkPlaneByCoordinate(IModelView* pModelView, const Coordinate3d& coord)
{
    OwnerPtr<IPlane> gpln = IPlane::Create(
        coord.GetOrigin(),
        coord.GetX(),
        coord.GetY()
    );
    pModelView->SetWorkPlane(gpln.get());
}
```

## GetClosestPointOnElement 用途

GetClosestPointOnElement 方法用于在图元上找到距离指定点最近的点，主要用途包括：

### 1. 拖拽起始点计算

```cpp
// 在拖拽开始时，计算图元上最接近鼠标点击位置的点
Vector3d GetClosestPointOnElement(const IElement* pElement, const Vector3d& mousePos)
{
    // 获取图元的几何表示
    const IGraphicsElementShape* pShape = pElement->GetElementModelShape()->GetGraphicsElementShape();

    // 计算最近点
    Vector3d closestPoint = CalculateClosestPoint(pShape, mousePos);

    return closestPoint;
}
```

### 2. 捕捉计算

- **端点捕捉**: 找到图元端点中距离鼠标最近的点
- **中点捕捉**: 计算图元中点位置
- **垂足捕捉**: 计算从鼠标位置到图元的垂足

### 3. 移动向量计算

```cpp
// 计算图元移动向量时使用最近点作为参考
Vector3d CalculateMoveVector(const IElement* pElement, const Vector3d& startPos, const Vector3d& endPos)
{
    Vector3d nearestStartPt = GetClosestPointOnElement(pElement, startPos);
    Vector3d moveVector = endPos - nearestStartPt;

    return moveVector;
}
```

### 4. 实际应用示例

在 GDMPLab 中的具体应用：
- **结构连接计算**: `GbmpJoinUtils::GetClosestEndPointJoinPosition()` 用于计算构件连接的最近端点位置
- **坐标转换**: `Coordinate3dUtils::GetClosestPointOnXOY()` 用于将三维点投影到 XOY 平面的最近点

## 拖拽状态管理

### 1. 拖拽相关成员变量

```cpp
class GbmpActionModify {
private:
    bool m_isCaught;                                // 捕获标记
    Vector3d m_nearStartPt;                         // 所移动Element上离初始点最近的点
    Vector3d m_interPt;                             // 交点
    Vector3d m_endPt;                               // 终点
    Vector3d m_collinearVec;                        // 最近点和定位线之间的向量
    
    std::vector<ElementId> m_moveElementIds;        // 待移动的图元ID列表
    
    // 影子对象管理
    typedef std::map<ElementId, ElementId> ElementToShadowMap;
    ElementToShadowMap m_elementShadows;            // 图元到影子对象的映射
    
    // 捕捉相关
    OwnerPtr<ISnapContext> m_oSnapContext;          // 捕捉上下文
    OwnerPtr<ISnapCandidates> m_oSnapCandidates;    // 捕捉候选项
};
```

### 2. 拖拽状态重置

```cpp
void GbmpActionModify::Reset()
{
    m_startPt = m_endPt = m_interPt = m_nearStartPt = Vector3d(0, 0, 0);
    
    m_isCaught = false;
    m_status = PS_GBMP_NOTSTART;
    
    m_moveElementIds.clear();
    
    ClearShadows();
    
    // 捕捉结束
    ISnapRender::Get()->ClearSnapRenderData();
}
```

## 拖拽流程

### 1. 拖拽开始 (OnLButtonDown)

```mermaid
sequenceDiagram
    participant User as 用户
    participant Action as GbmpActionModify
    participant Base as GbmpPickActionBase
    participant Behavior as IActionModifyBehavior
    participant Snap as ISnapContext
    
    User->>Action: OnLButtonDown()
    Action->>Base: OnLButtonDown()
    Action->>Action: CollectMoveElements()
    Action->>Action: ProjectPositionOnWorkPlane()
    Action->>Action: GetNearestPoint()
    Action->>Snap: Create()
    Action->>Snap: InitSnapContextFromUiView()
    Action->>Snap: SetDataFromMovingElements()
```

#### 关键步骤详解

```cpp
bool GbmpActionModify::OnLButtonDown(IUiView* pCurrentView, const Vector3d& pos)
{
    // 1. 调用基类处理拾取
    GbmpPickActionBase::OnLButtonDown(pCurrentView, pos);
    
    // 2. 计算与工作平面的交点
    Vector3d posOnWP;
    IDocument* pDoc = pCurrentView->GetUiDocument()->GetDbDocument();
    
    // 3. 准备移动的对象
    CollectMoveElements(pDoc);
    
    // 4. 投影到工作平面
    if (ActionBase::ProjectPositionOnWorkPlane(pCurrentView, pos, posOnWP))
    {
        // 处理特殊对象（如裁剪框夹点）
        if (m_auxElementId != ElementId::InvalidID)
        {
            const IElementShapeHandle* pShapeHandle = 
                quick_cast<IElementShapeHandle>(pDoc->GetElement(m_auxElementId));
            if (pShapeHandle)
            {
                const IViewClipRange* pViewClipRange = 
                    quick_cast<IViewClipRange>(pDoc->GetElement(pShapeHandle->GetMasterId()));
                if (pViewClipRange && pViewClipRange->GetSplitType() != EnSplitType::Unknown)
                {
                    posOnWP = pCurrentView->GetCanvas()->MapFromSubView(posOnWP);
                }
            }
        }
        
        m_startPt = m_endPt = m_interPt = posOnWP;
    }
    
    // 5. 检查是否由其他Action处理
    if (HandledByOtherAction(pDoc, pos))
    {
        Reset();
        return true;
    }
    
    // 6. 创建捕捉上下文
    m_oSnapContext = ISnapContext::Create();
    GetNearestPoint(pCurrentView, m_startPt, m_nearStartPt);
    
    // 7. 设置捕捉过滤器
    SetupSnapFilters();
    
    // 8. 初始化捕捉上下文
    m_oSnapContext->SetInputPoint(pos);
    UiSnapUtils::InitSnapContextFromUiView(m_oSnapContext.get(), pCurrentView, 
                                          PickPointExchangeData::SnapPlaneType::WorkPlane);
    m_oSnapContext->SetFilterForLocalSnap(GetFilterForLocalSnap());
    m_oSnapContext->SetFilterForRemoteSnap(GetFilterForRemoteSnap());
    
    // 9. 设置移动数据
    m_oSnapContext->SetDataFromMovingElements(m_moveElementIds, m_nearStartPt);
    
    return true;
}
```

### 2. 拖拽过程 (OnMovePoint)

```cpp
bool GbmpActionModify::OnMovePoint(IUiView* pCurrentView, const Vector3d& pos)
{
    if (!IsMoving())
        return GbmpPickActionBase::OnMovePoint(pCurrentView, pos);
        
    return OnMovePoint(pCurrentView);
}

bool GbmpActionModify::OnMovePoint(IUiView* pCurrentView)
{
    IDocument* pDoc = pCurrentView->GetUiDocument()->GetDbDocument();
    
    // 1. 执行捕捉
    SnapPoint(pCurrentView, m_endPt);
    
    // 2. 计算移动向量
    Vector3d moveVector = m_endPt - m_startPt;
    
    // 3. 更新影子对象
    if (m_opModifyElementsBehavior)
    {
        for (const ElementId& elementId : m_moveElementIds)
        {
            // 创建或更新影子对象
            IElement* pShadow = GetShadow(pCurrentView, pDoc->GetElement(elementId));
            if (pShadow)
            {
                // 修改影子对象
                m_opModifyElementsBehavior->ModifyElement(pCurrentView, elementId, 
                                                        m_startPt, moveVector);
            }
        }
        
        // 处理夹点拖拽
        if (m_auxElementId.IsValid())
        {
            m_opModifyElementsBehavior->ModifyGripPoint(pCurrentView, m_auxElementId, 
                                                      m_startPt, moveVector);
        }
    }
    
    // 4. 更新视图
    UpdateView();
    return true;
}
```

### 3. 拖拽结束 (OnLButtonUp)

```cpp
bool GbmpActionModify::OnLButtonUp(IUiView* pCurrentView, const Vector3d& pos)
{
    if (!IsMoving())
        return GbmpPickActionBase::OnLButtonUp(pCurrentView, pos);
        
    IDocument* pDoc = pCurrentView->GetUiDocument()->GetDbDocument();
    
    // 1. 计算最终移动向量
    Vector3d moveVector = m_endPt - m_startPt;
    
    // 2. 执行实际修改
    if (m_opModifyElementsBehavior)
    {
        // 开始事务
        if (!m_upUserTransaction)
            m_upUserTransaction = IUserTransaction::Create(pDoc);
        m_upUserTransaction->Start();
        
        try
        {
            // 修改图元
            for (const ElementId& elementId : m_moveElementIds)
            {
                m_opModifyElementsBehavior->ModifyElement(pCurrentView, elementId, 
                                                        m_startPt, moveVector);
            }
            
            // 修改夹点
            if (m_auxElementId.IsValid())
            {
                m_opModifyElementsBehavior->ModifyGripPoint(pCurrentView, m_auxElementId, 
                                                          m_startPt, moveVector);
            }
            
            // 后处理
            m_opModifyElementsBehavior->ProcessPostModify(pCurrentView, m_moveElementIds, 
                                                        m_oSnapCandidates.get());
            
            // 提交事务
            m_upUserTransaction->Commit();
        }
        catch (...)
        {
            // 回滚事务
            m_upUserTransaction->Rollback();
        }
    }
    
    // 3. 清理状态
    Reset();
    OnSelectionChanged();
    UpdateView();
    
    return true;
}
```

## 影子对象管理

### 1. 影子对象创建

```cpp
IElement* GbmpActionModify::GetShadow(IUiView* pCurrentView, const IElement* pElement)
{
    if (!pElement)
        return nullptr;
        
    ElementId elementId = pElement->GetElementId();
    
    // 检查是否已存在影子对象
    auto iter = m_elementShadows.find(elementId);
    if (iter != m_elementShadows.end())
    {
        return pElement->GetDocument()->GetElement(iter->second);
    }
    
    // 创建新的影子对象
    if (m_opModifyElementsBehavior)
    {
        IElement* pShadow = m_opModifyElementsBehavior->CreateElementShadow(pCurrentView, elementId);
        if (pShadow)
        {
            m_elementShadows[elementId] = pShadow->GetElementId();
            return pShadow;
        }
    }
    
    return nullptr;
}
```

### 2. 影子对象清理

```cpp
bool GbmpActionModify::ClearShadows()
{
    IDocument* pDoc = GetDoc();
    if (!pDoc)
        return false;
        
    for (auto& pair : m_elementShadows)
    {
        ElementId shadowId = pair.second;
        if (shadowId.IsValid())
        {
            pDoc->DeleteElement(shadowId);
        }
    }
    
    m_elementShadows.clear();
    return true;
}
```

## 移动对象收集

### 1. CollectMoveElements 方法

```cpp
void GbmpActionModify::CollectMoveElements(IDocument* pDoc)
{
    m_moveElementIds.clear();
    
    // 获取当前选择集
    const GraphicsNodeReferenceOwnerPtrSet& selections = ISelection::Get()->GetGraphicsNodeReferences();
    
    for (const auto& selection : selections)
    {
        ElementId elementId = selection->GetElementId();
        
        // 检查是否为可移动的图元
        const IElement* pElement = pDoc->GetElement(elementId);
        if (pElement && IsElementMovable(pElement))
        {
            m_moveElementIds.push_back(elementId);
        }
    }
    
    // 如果没有选中的可移动图元，检查辅助对象
    if (m_moveElementIds.empty() && m_auxElementId.IsValid())
    {
        const IElement* pAuxElement = pDoc->GetElement(m_auxElementId);
        if (pAuxElement)
        {
            // 获取辅助对象的主对象
            if (const IElementShapeHandle* pShapeHandle = quick_cast<IElementShapeHandle>(pAuxElement))
            {
                ElementId masterId = pShapeHandle->GetMasterId();
                if (masterId.IsValid())
                {
                    m_moveElementIds.push_back(masterId);
                }
            }
        }
    }
}
```

## 捕捉处理

### 1. 捕捉点计算

```cpp
void GbmpActionModify::SnapPoint(IUiView* pCurrentView, const Vector3d& pos)
{
    if (!m_oSnapContext)
        return;
        
    // 设置输入点
    m_oSnapContext->SetInputPoint(pos);
    
    // 执行捕捉
    m_oSnapCandidates = ISnap::Get()->GetSnapCandidates(m_oSnapContext.get());
    
    if (m_oSnapCandidates && m_oSnapCandidates->GetCount() > 0)
    {
        // 获取最佳捕捉点
        const ISnapCandidate* pBestCandidate = m_oSnapCandidates->GetBestCandidate();
        if (pBestCandidate)
        {
            m_endPt = pBestCandidate->GetSnapPoint();
            m_isCaught = true;
        }
    }
    else
    {
        // 没有捕捉到，使用原始点
        Vector3d posOnWP;
        if (ActionBase::ProjectPositionOnWorkPlane(pCurrentView, pos, posOnWP))
        {
            m_endPt = posOnWP;
        }
        m_isCaught = false;
    }
}
```

### 2. 最近点计算

```cpp
void GbmpActionModify::GetNearestPoint(IUiView* pCurrentView, 
                                      const Vector3d& inputPoint, 
                                      Vector3d& nearPoint)
{
    nearPoint = inputPoint;
    
    if (m_moveElementIds.empty())
        return;
        
    IDocument* pDoc = pCurrentView->GetUiDocument()->GetDbDocument();
    double minDistance = std::numeric_limits<double>::max();
    
    for (const ElementId& elementId : m_moveElementIds)
    {
        const IElement* pElement = pDoc->GetElement(elementId);
        if (!pElement)
            continue;
            
        // 获取图元上最近的点
        Vector3d closestPoint;
        if (GetClosestPointOnElement(pElement, inputPoint, closestPoint))
        {
            double distance = (closestPoint - inputPoint).Length();
            if (distance < minDistance)
            {
                minDistance = distance;
                nearPoint = closestPoint;
            }
        }
    }
}
```

## 特殊对象处理

### 1. 夹点拖拽

```cpp
// 夹点拖拽的特殊处理
if (m_auxElementId.IsValid())
{
    const IElementShapeHandle* pShapeHandle = 
        quick_cast<IElementShapeHandle>(pDoc->GetElement(m_auxElementId));
    if (pShapeHandle)
    {
        // 获取夹点行为
        const IElementShapeHandleBehavior* pBehavior = pShapeHandle->GetElementShapeHandleBehavior();
        const IGripPointsShapeHandleBehavior* pGripBehavior = 
            quick_cast<IGripPointsShapeHandleBehavior>(pBehavior);
            
        if (pGripBehavior)
        {
            // 获取捕捉优先级类型
            std::vector<Int64> priorityTypes = pGripBehavior->GetSnapPriorityTypes();
            if (!priorityTypes.empty())
            {
                SetSnapCandidatesPreprocessor(
                    NEW_AS_OWNER_PTR(GbmpSnapEndPointPriorityTypesPreprocessor, priorityTypes));
                m_oSnapContext->SetSnapCandidatesPreprocessor(GetSnapCandidatesPreprocessor());
            }
        }
    }
}
```

### 2. 裁剪框处理

```cpp
// 裁剪框的特殊坐标转换
const IViewClipRange* pViewClipRange = quick_cast<IViewClipRange>(pDoc->GetElement(elementId));
if (pViewClipRange && pViewClipRange->GetSplitType() != EnSplitType::Unknown)
{
    posOnWP = pCurrentView->GetCanvas()->MapFromSubView(posOnWP);
}
```

## 性能优化

### 1. 捕捉过滤器优化

```cpp
// 设置排除的图元ID，避免自己捕捉自己
GbmpDefaultPickFilterForLocalSnap* pLocalFilter = 
    dynamic_cast<GbmpDefaultPickFilterForLocalSnap*>(m_oFilterForLocalSnap.get());
if (pLocalFilter)
    pLocalFilter->SetExcludedElementIds(m_moveElementIds);
    
GbmpDefaultPickFilterForRemoteSnap* pRemoteFilter = 
    dynamic_cast<GbmpDefaultPickFilterForRemoteSnap*>(m_oFilterForRemoteSnap.get());
if (pRemoteFilter)
    pRemoteFilter->SetExcludedElementIds(m_moveElementIds);
```

### 2. 视图更新优化

```cpp
// 只在必要时更新视图
if (moveVector.Length() > Constants::LENGTH_EPS)
{
    UpdateView();
}
```

## 错误处理

### 1. 事务管理

```cpp
try
{
    // 执行修改操作
    m_opModifyElementsBehavior->ModifyElement(/* ... */);
    m_upUserTransaction->Commit();
}
catch (const std::exception& e)
{
    // 记录错误并回滚
    DBG_WARN(L"修改操作失败: " + std::wstring(e.what()), L"GDMPLab", L"2024-03-30");
    if (m_upUserTransaction && m_upUserTransaction->IsStarted())
        m_upUserTransaction->Rollback();
}
```

### 2. 状态一致性

```cpp
// 确保在异常情况下也能正确清理状态
void GbmpActionModify::ActionCancelled()
{
    Reset();
    ClearShadows();
    GbmpPickActionBase::ActionCancelled();
}
```
